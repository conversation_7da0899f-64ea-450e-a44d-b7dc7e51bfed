<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限卡片测试</title>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
            padding: 20px;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        /* 权限卡片样式 */
        .permission-cards-container {
            display: flex !important;
            gap: 20px !important;
            flex-wrap: wrap !important;
            margin-top: 10px !important;
        }
        
        .permission-card {
            width: 180px !important;
            height: 180px !important;
            background: rgba(255, 255, 255, 0.1) !important;
            border: 2px solid rgba(255, 255, 255, 0.2) !important;
            border-radius: 15px !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            position: relative !important;
            overflow: hidden !important;
        }
        
        .permission-card.selected {
            background: rgba(138, 43, 226, 0.3) !important;
            border-color: rgba(138, 43, 226, 0.5) !important;
            box-shadow: 0 0 20px rgba(138, 43, 226, 0.3) !important;
        }
        
        .permission-card-content {
            text-align: center !important;
            color: white !important;
            z-index: 2 !important;
            position: relative !important;
        }
        
        .permission-title {
            font-size: 18px !important;
            font-weight: 600 !important;
            margin-bottom: 8px !important;
            line-height: 1.2 !important;
        }
        
        .permission-subtitle {
            font-size: 16px !important;
            font-weight: 500 !important;
            opacity: 0.9 !important;
            line-height: 1.2 !important;
        }
        
        .debug-info {
            margin-top: 30px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }
        
        .debug-info h3 {
            margin-top: 0;
        }
        
        .debug-info p {
            margin: 5px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>权限卡片功能测试</h1>
        
        <div class="form-group">
            <label>功能权限</label>
            <div class="permission-cards-container">
                <div class="permission-card" id="wechat_store_card"
                     data-permission="wechat_store" onclick="togglePermissionCard(this, 'has_wechat_store')">
                    <div class="permission-card-content">
                        <div class="permission-title">小梅花AI客服</div>
                        <div class="permission-subtitle">微信小店</div>
                    </div>
                </div>
                <div class="permission-card" id="douyin_store_card"
                     data-permission="douyin_store" onclick="togglePermissionCard(this, 'has_douyin_store')">
                    <div class="permission-card-content">
                        <div class="permission-title">小梅花AI客服</div>
                        <div class="permission-subtitle">抖店</div>
                    </div>
                </div>
            </div>
            
            <!-- 隐藏的input用于表单提交 -->
            <input type="hidden" name="has_wechat_store" id="has_wechat_store" value="0">
            <input type="hidden" name="has_douyin_store" id="has_douyin_store" value="0">
        </div>
        
        <div class="debug-info">
            <h3>调试信息</h3>
            <p>微信小店权限: <span id="wechat_value">0</span></p>
            <p>抖店权限: <span id="douyin_value">0</span></p>
            <p>点击状态: <span id="click_log">无</span></p>
        </div>
    </div>

    <script>
        // 权限卡片切换功能
        function togglePermissionCard(cardElement, inputName) {
            const hiddenInput = document.getElementById(inputName);
            if (!hiddenInput) {
                console.error('找不到隐藏输入框:', inputName);
                return;
            }
            
            const isSelected = cardElement.classList.contains('selected');
            
            if (isSelected) {
                // 取消选中
                cardElement.classList.remove('selected');
                hiddenInput.value = '0';
            } else {
                // 选中
                cardElement.classList.add('selected');
                hiddenInput.value = '1';
            }
            
            console.log('权限卡片状态已更新:', inputName, '=', hiddenInput.value);
            
            // 更新调试信息
            updateDebugInfo();
        }
        
        function updateDebugInfo() {
            document.getElementById('wechat_value').textContent = document.getElementById('has_wechat_store').value;
            document.getElementById('douyin_value').textContent = document.getElementById('has_douyin_store').value;
            document.getElementById('click_log').textContent = new Date().toLocaleTimeString();
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('权限卡片测试页面已初始化');
            updateDebugInfo();
        });
    </script>
</body>
</html>
