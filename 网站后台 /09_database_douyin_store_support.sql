-- =====================================================
-- 数据库升级脚本：抖店支持功能
-- 版本：v1.0
-- 日期：2025-08-11
-- 描述：为卡密管理系统添加抖店店铺支持
-- =====================================================

-- 1. 为 license_keys 表添加抖店相关字段
ALTER TABLE license_keys ADD COLUMN douyin_store_name TEXT DEFAULT NULL;
ALTER TABLE license_keys ADD COLUMN douyin_store_id TEXT DEFAULT NULL;

-- 2. 为 license_key_stores 表添加店铺类型字段（用于区分微信店铺和抖店）
ALTER TABLE license_key_stores ADD COLUMN store_type TEXT DEFAULT 'wechat';
ALTER TABLE license_key_stores ADD COLUMN douyin_store_name TEXT DEFAULT NULL;
ALTER TABLE license_key_stores ADD COLUMN douyin_store_id TEXT DEFAULT NULL;

-- 3. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_license_keys_douyin_store_id ON license_keys(douyin_store_id);
CREATE INDEX IF NOT EXISTS idx_license_key_stores_store_type ON license_key_stores(store_type);
CREATE INDEX IF NOT EXISTS idx_license_key_stores_douyin_store_id ON license_key_stores(douyin_store_id);

-- 4. 更新现有数据，将所有现有的额外店铺标记为微信店铺类型
UPDATE license_key_stores SET store_type = 'wechat' WHERE store_type IS NULL;

-- 5. 验证数据完整性
-- 检查是否有卡密同时拥有微信和抖店功能但缺少对应店铺信息的情况
-- 这个查询应该返回空结果，如果有结果说明数据不完整
SELECT 
    id, 
    key_value,
    has_customer_service,
    has_product_listing,
    store_name,
    wechat_store_id,
    douyin_store_name,
    douyin_store_id
FROM license_keys 
WHERE 
    (has_customer_service = 1 AND (store_name IS NULL OR wechat_store_id IS NULL))
    OR 
    (has_product_listing = 1 AND (douyin_store_name IS NULL OR douyin_store_id IS NULL));

-- 6. 创建视图以便于查询卡密的完整店铺信息
CREATE VIEW IF NOT EXISTS v_license_keys_with_stores AS
SELECT 
    lk.id,
    lk.key_value,
    lk.type,
    lk.status,
    lk.expiry_date,
    lk.has_customer_service,
    lk.has_product_listing,
    lk.is_multi_store,
    lk.created_at,
    -- 微信店铺信息
    lk.store_name as wechat_store_name,
    lk.wechat_store_id,
    -- 抖店信息
    lk.douyin_store_name,
    lk.douyin_store_id,
    -- 额外店铺数量
    (SELECT COUNT(*) FROM license_key_stores lks WHERE lks.license_key_id = lk.id) as additional_stores_count
FROM license_keys lk;

-- 7. 创建函数以获取卡密的所有店铺信息（包括主店铺和额外店铺）
-- 注意：SQLite不支持存储过程，这里提供查询示例
-- 获取指定卡密的所有店铺信息的查询示例：
/*
SELECT 
    'main' as store_category,
    'wechat' as store_type,
    lk.store_name as store_name,
    lk.wechat_store_id as store_id,
    NULL as douyin_store_name,
    NULL as douyin_store_id
FROM license_keys lk 
WHERE lk.id = ? AND lk.has_customer_service = 1 AND lk.store_name IS NOT NULL

UNION ALL

SELECT 
    'main' as store_category,
    'douyin' as store_type,
    NULL as store_name,
    NULL as store_id,
    lk.douyin_store_name,
    lk.douyin_store_id
FROM license_keys lk 
WHERE lk.id = ? AND lk.has_product_listing = 1 AND lk.douyin_store_name IS NOT NULL

UNION ALL

SELECT 
    'additional' as store_category,
    lks.store_type,
    CASE WHEN lks.store_type = 'wechat' THEN lks.store_name ELSE NULL END as store_name,
    CASE WHEN lks.store_type = 'wechat' THEN lks.wechat_store_id ELSE NULL END as store_id,
    CASE WHEN lks.store_type = 'douyin' THEN lks.douyin_store_name ELSE NULL END as douyin_store_name,
    CASE WHEN lks.store_type = 'douyin' THEN lks.douyin_store_id ELSE NULL END as douyin_store_id
FROM license_key_stores lks 
WHERE lks.license_key_id = ?

ORDER BY store_category, store_type;
*/

-- 8. 数据完整性约束（SQLite支持的约束）
-- 确保如果has_customer_service=1，则必须有微信店铺信息
-- 确保如果has_product_listing=1，则必须有抖店信息
-- 注意：SQLite的CHECK约束在某些版本中可能不完全支持，建议在应用层进行验证

-- 9. 升级完成标记
-- 可以在应用中检查这些字段是否存在来判断是否已经升级
-- SELECT sql FROM sqlite_master WHERE type='table' AND name='license_keys';

-- =====================================================
-- 升级脚本执行完成
-- 
-- 新增功能：
-- 1. 支持抖店店铺信息存储
-- 2. 支持多店铺类型（微信/抖店）
-- 3. 改进的数据查询视图
-- 4. 数据完整性验证
-- 
-- 注意事项：
-- 1. 执行前请备份数据库
-- 2. 确保应用程序已更新以支持新字段
-- 3. 验证现有卡密功能正常工作
-- =====================================================
