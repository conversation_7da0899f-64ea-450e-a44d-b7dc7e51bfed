# 🛍️ 卡密管理抖店功能优化说明

## 📋 优化概述

根据需求，对网站后台的卡密管理功能进行了全面优化，新增了抖店支持，并改进了用户界面和操作体验。

## ✅ 主要优化内容

### 1. 🏷️ **店铺名称标签优化**
- ✅ 将原有的"店铺名称"改为"**微信店铺名称**"
- ✅ 明确区分微信小店和抖店的店铺信息
- ✅ 保持界面一致性和用户理解的清晰度

### 2. 🆕 **抖店功能支持**
- ✅ **新增抖店店铺名称字段**：支持输入抖店的店铺名称
- ✅ **新增抖店ID字段**：支持输入抖店的唯一标识ID
- ✅ **智能显示逻辑**：当选择"抖店"卡密功能时，自动显示抖店信息输入框
- ✅ **必填验证**：选择抖店功能时，抖店信息为必填项

### 3. 🔄 **新增店铺按钮优化**
- ✅ 将原有的"新增店铺"按钮拆分为两个独立按钮：
  - **新增微信店铺**：添加额外的微信小店信息
  - **新增抖店店铺**：添加额外的抖店信息
- ✅ 支持混合添加不同类型的店铺
- ✅ 每个店铺类型都有明确的标识和字段

## 🔧 **技术实现详情**

### 数据库结构优化

#### 主表字段新增
```sql
-- license_keys 表新增字段
ALTER TABLE license_keys ADD COLUMN douyin_store_name TEXT DEFAULT NULL;
ALTER TABLE license_keys ADD COLUMN douyin_store_id TEXT DEFAULT NULL;
```

#### 关联表优化
```sql
-- license_key_stores 表新增字段
ALTER TABLE license_key_stores ADD COLUMN store_type TEXT DEFAULT 'wechat';
ALTER TABLE license_key_stores ADD COLUMN douyin_store_name TEXT DEFAULT NULL;
ALTER TABLE license_key_stores ADD COLUMN douyin_store_id TEXT DEFAULT NULL;
```

### 前端界面优化

#### 1. **抖店信息区域**
```html
<!-- 抖店信息区域 - 当选择抖店功能时显示 -->
<div id="douyin-store-section" class="form-row" style="display: none;">
    <div class="form-group">
        <label><i class="fas fa-store"></i> 抖店店铺名称 <span style="color: #ff6b6b;">*</span></label>
        <input type="text" name="douyin_store_name" placeholder="请输入抖店店铺名称">
    </div>
    <div class="form-group">
        <label><i class="fas fa-id-card"></i> 抖店ID <span style="color: #ff6b6b;">*</span></label>
        <input type="text" name="douyin_store_id" placeholder="请输入抖店ID">
    </div>
</div>
```

#### 2. **智能显示控制**
```javascript
// 处理抖店信息区域的显示/隐藏
function handleDouyinStoreSection() {
    const douyinStoreSection = document.getElementById('douyin-store-section');
    const hasProductListing = document.getElementById('has_product_listing');
    
    if (hasProductListing && hasProductListing.value === '1') {
        douyinStoreSection.style.display = 'flex';
        // 设置为必填
        const douyinInputs = douyinStoreSection.querySelectorAll('input[type="text"]');
        douyinInputs.forEach(input => {
            input.setAttribute('required', 'required');
        });
    } else {
        douyinStoreSection.style.display = 'none';
        // 清空并移除必填
        const douyinInputs = douyinStoreSection.querySelectorAll('input[type="text"]');
        douyinInputs.forEach(input => {
            input.removeAttribute('required');
            input.value = '';
        });
    }
}
```

#### 3. **新增店铺按钮**
```html
<div style="margin-bottom: 20px;">
    <button type="button" id="add-wechat-store-btn" class="btn btn-secondary glass-btn" style="margin-right: 10px;">
        <i class="fas fa-plus-circle"></i> 新增微信店铺
    </button>
    <button type="button" id="add-douyin-store-btn" class="btn btn-secondary glass-btn">
        <i class="fas fa-plus-circle"></i> 新增抖店店铺
    </button>
</div>
```

### 后端逻辑优化

#### 1. **数据验证增强**
```php
// 验证抖店信息
if ($has_product_listing && (empty($douyin_store_name) || empty($douyin_store_id))) {
    $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 选择抖店功能时，抖店店铺名称和抖店ID为必填项</div>";
}
```

#### 2. **数据库操作优化**
```php
// 插入主卡密记录（包含抖店信息）
$stmt = $pdo->prepare(
    "INSERT INTO license_keys (key_value, type, store_name, wechat_store_id, douyin_store_name, douyin_store_id, expiry_date, status, has_customer_service, has_product_listing, is_multi_store, created_at)
     VALUES (?, ?, ?, ?, ?, ?, ?, 'active', ?, ?, ?, NOW())"
);
```

## 🎯 **功能特性**

### 1. **智能表单控制**
- ✅ **条件显示**：只有选择抖店功能时才显示抖店信息输入框
- ✅ **动态验证**：根据选择的功能动态设置必填字段
- ✅ **实时反馈**：选择/取消功能时立即显示/隐藏相关字段

### 2. **多店铺类型支持**
- ✅ **微信店铺**：支持微信小店的店铺名称和ID
- ✅ **抖店店铺**：支持抖店的店铺名称和ID
- ✅ **混合支持**：一个卡密可以同时支持多个不同类型的店铺

### 3. **用户体验优化**
- ✅ **清晰标识**：明确区分不同类型的店铺信息
- ✅ **操作便捷**：独立的新增按钮，操作更直观
- ✅ **错误提示**：详细的验证错误信息，帮助用户正确填写

### 4. **数据完整性**
- ✅ **强制验证**：选择对应功能时必须填写相关店铺信息
- ✅ **数据关联**：正确关联卡密功能与店铺信息
- ✅ **类型标识**：清楚标识每个店铺的类型（微信/抖店）

## 🚀 **使用流程**

### 生成新卡密
1. **填写微信店铺信息**：
   - 微信店铺名称（必填）
   - 微信小店ID（必填）

2. **选择卡密功能**：
   - 选择"小梅花AI客服 - 微信小店"：仅需微信店铺信息
   - 选择"小梅花AI客服 - 抖店"：自动显示抖店信息输入框

3. **填写抖店信息**（如果选择了抖店功能）：
   - 抖店店铺名称（必填）
   - 抖店ID（必填）

4. **添加额外店铺**（可选）：
   - 点击"新增微信店铺"：添加更多微信小店
   - 点击"新增抖店店铺"：添加更多抖店

### 编辑现有卡密
1. **基本信息修改**：
   - 修改微信店铺名称和ID
   - 根据功能选择显示/隐藏抖店信息

2. **功能调整**：
   - 切换卡密功能时自动显示/隐藏对应的店铺信息区域
   - 实时验证必填字段

3. **店铺管理**：
   - 使用不同按钮添加不同类型的店铺
   - 每个店铺都有明确的类型标识

## 📊 **数据结构**

### 主卡密表 (license_keys)
```
- id: 卡密ID
- key_value: 卡密值
- store_name: 微信店铺名称
- wechat_store_id: 微信小店ID
- douyin_store_name: 抖店店铺名称 (新增)
- douyin_store_id: 抖店ID (新增)
- has_customer_service: 是否有微信小店功能
- has_product_listing: 是否有抖店功能
- ...其他字段
```

### 额外店铺表 (license_key_stores)
```
- id: 记录ID
- license_key_id: 关联的卡密ID
- store_type: 店铺类型 ('wechat' 或 'douyin') (新增)
- store_name: 微信店铺名称
- wechat_store_id: 微信小店ID
- douyin_store_name: 抖店店铺名称 (新增)
- douyin_store_id: 抖店ID (新增)
```

## 🔄 **升级步骤**

### 1. 数据库升级
```bash
# 执行数据库升级脚本
mysql -u username -p database_name < 09_database_douyin_store_support.sql
```

### 2. 代码部署
- ✅ 上传更新后的 `keys.php` 文件
- ✅ 确保所有新增的JavaScript函数正常工作
- ✅ 验证CSS样式正确加载

### 3. 功能测试
- ✅ 测试新增卡密功能
- ✅ 测试编辑卡密功能
- ✅ 测试抖店信息的显示/隐藏逻辑
- ✅ 测试表单验证功能

## 🎉 **优化成果**

### 用户体验提升
1. **界面更清晰**：明确区分微信小店和抖店
2. **操作更直观**：独立的新增按钮，功能明确
3. **验证更智能**：根据选择动态调整必填字段

### 功能更完善
1. **全面支持抖店**：从数据库到界面的完整支持
2. **多店铺管理**：支持混合管理不同类型的店铺
3. **数据完整性**：确保功能选择与店铺信息的一致性

### 技术架构优化
1. **数据库结构合理**：新增字段不影响现有功能
2. **代码逻辑清晰**：前后端分离，职责明确
3. **扩展性良好**：为未来添加更多平台预留空间

现在网站后台的卡密管理功能已经完全支持抖店，用户可以方便地管理微信小店和抖店的卡密，享受更完善的多平台管理体验！🚀
