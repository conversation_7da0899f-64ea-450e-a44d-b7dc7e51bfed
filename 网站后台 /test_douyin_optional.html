<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖店可选功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        .form-group {
            flex: 1;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .permission-cards-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .permission-card {
            width: 150px;
            height: 100px;
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .permission-card.selected {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        .permission-card-content {
            text-align: center;
        }
        .permission-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .permission-subtitle {
            font-size: 12px;
            color: #666;
        }
        .required {
            color: #ff6b6b;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🛍️ 店铺信息可选功能测试页面</h1>

    <div class="test-card">
        <h2>功能说明</h2>
        <div class="status info">
            <strong>测试目标：</strong>验证微信店铺和抖店信息都默认为可选，只有选择对应功能时才变为必填
        </div>

        <h3>预期行为：</h3>
        <ul>
            <li>✅ 微信店铺信息区域默认显示，但字段为可选（无红色*标记）</li>
            <li>✅ 选择"小梅花AI客服-微信小店"时，微信店铺字段立即变为必填（显示红色*标记）</li>
            <li>✅ 抖店信息区域默认显示，但字段为可选（无红色*标记）</li>
            <li>✅ 选择"小梅花AI客服-抖店"时，抖店字段立即变为必填（显示红色*标记）</li>
            <li>✅ 取消选择功能时，对应字段恢复为可选状态</li>
            <li>✅ 表单验证只在选择对应功能时才验证对应店铺信息</li>
        </ul>
    </div>

    <div class="test-card">
        <h2>测试表单</h2>
        <form id="testForm">
            <!-- 微信店铺信息区域 -->
            <div id="wechat-store-section" class="form-row">
                <div class="form-group">
                    <label>微信店铺名称 <span id="wechat-store-name-required" class="required" style="display: none;">*</span></label>
                    <input type="text" name="store_name" placeholder="请输入微信店铺名称（可选）">
                </div>
                <div class="form-group">
                    <label>微信小店ID <span id="wechat-store-id-required" class="required" style="display: none;">*</span></label>
                    <input type="text" name="wechat_store_id" placeholder="请输入微信小店ID（可选）">
                </div>
            </div>

            <!-- 抖店信息区域 -->
            <div id="douyin-store-section" class="form-row">
                <div class="form-group">
                    <label>抖店店铺名称 <span id="douyin-store-name-required" class="required" style="display: none;">*</span></label>
                    <input type="text" name="douyin_store_name" placeholder="请输入抖店店铺名称（可选）">
                </div>
                <div class="form-group">
                    <label>抖店ID <span id="douyin-store-id-required" class="required" style="display: none;">*</span></label>
                    <input type="text" name="douyin_store_id" placeholder="请输入抖店ID（可选）">
                </div>
            </div>

            <!-- 卡密功能选择 -->
            <div class="form-group">
                <label>卡密功能</label>
                <div class="permission-cards-container">
                    <div class="permission-card" data-permission="customer_service" onclick="togglePermissionCard(this, 'has_customer_service')">
                        <div class="permission-card-content">
                            <div class="permission-title">小梅花AI客服</div>
                            <div class="permission-subtitle">微信小店</div>
                        </div>
                    </div>
                    <div class="permission-card" data-permission="product_listing" onclick="togglePermissionCard(this, 'has_product_listing')">
                        <div class="permission-card-content">
                            <div class="permission-title">小梅花AI客服</div>
                            <div class="permission-subtitle">抖店</div>
                        </div>
                    </div>
                </div>

                <!-- 隐藏的input用于表单提交 -->
                <input type="hidden" name="has_customer_service" id="has_customer_service" value="0">
                <input type="hidden" name="has_product_listing" id="has_product_listing" value="0">
            </div>

            <button type="button" onclick="testValidation()">测试表单验证</button>
            <button type="button" onclick="resetForm()">重置表单</button>
        </form>
    </div>

    <div class="test-card">
        <h2>测试结果</h2>
        <div id="testResults"></div>
    </div>

    <script>
        // 权限卡片切换功能
        function togglePermissionCard(cardElement, inputName) {
            const hiddenInput = document.getElementById(inputName);
            if (!hiddenInput) {
                console.error('找不到隐藏输入框:', inputName);
                return;
            }

            const isSelected = cardElement.classList.contains('selected');

            if (isSelected) {
                // 取消选中
                cardElement.classList.remove('selected');
                hiddenInput.value = '0';
            } else {
                // 选中
                cardElement.classList.add('selected');
                hiddenInput.value = '1';
            }

            // 处理店铺信息区域的必填状态
            handleStoreSection();

            console.log('权限卡片状态已更新:', inputName, '=', hiddenInput.value);
            logTestResult(`功能选择更新: ${inputName} = ${hiddenInput.value}`);
        }

        // 处理店铺信息区域的必填状态（微信店铺和抖店）
        function handleStoreSection() {
            // 处理微信店铺信息区域
            const wechatStoreSection = document.getElementById('wechat-store-section');
            const hasCustomerService = document.getElementById('has_customer_service');

            if (wechatStoreSection && hasCustomerService) {
                const isWechatSelected = hasCustomerService.value === '1';

                // 微信店铺信息区域始终显示，只改变必填状态
                const wechatInputs = wechatStoreSection.querySelectorAll('input[type="text"]');
                const wechatStoreNameRequired = document.getElementById('wechat-store-name-required');
                const wechatStoreIdRequired = document.getElementById('wechat-store-id-required');

                if (isWechatSelected) {
                    // 设置为必填
                    wechatInputs.forEach(input => {
                        input.setAttribute('required', 'required');
                        input.placeholder = input.placeholder.replace('（可选）', '');
                    });
                    // 显示必填标记
                    if (wechatStoreNameRequired) wechatStoreNameRequired.style.display = 'inline';
                    if (wechatStoreIdRequired) wechatStoreIdRequired.style.display = 'inline';
                    logTestResult('✅ 微信店铺字段设为必填，显示红色*标记');
                } else {
                    // 设置为可选
                    wechatInputs.forEach(input => {
                        input.removeAttribute('required');
                        if (!input.placeholder.includes('（可选）')) {
                            input.placeholder = input.placeholder + '（可选）';
                        }
                    });
                    // 隐藏必填标记
                    if (wechatStoreNameRequired) wechatStoreNameRequired.style.display = 'none';
                    if (wechatStoreIdRequired) wechatStoreIdRequired.style.display = 'none';
                    logTestResult('✅ 微信店铺字段设为可选，隐藏红色*标记');
                }
            }

            // 处理抖店信息区域
            const douyinStoreSection = document.getElementById('douyin-store-section');
            const hasProductListing = document.getElementById('has_product_listing');

            if (douyinStoreSection && hasProductListing) {
                const isDouyinSelected = hasProductListing.value === '1';

                // 抖店信息区域始终显示，只改变必填状态
                const douyinInputs = douyinStoreSection.querySelectorAll('input[type="text"]');
                const douyinStoreNameRequired = document.getElementById('douyin-store-name-required');
                const douyinStoreIdRequired = document.getElementById('douyin-store-id-required');

                if (isDouyinSelected) {
                    // 设置为必填
                    douyinInputs.forEach(input => {
                        input.setAttribute('required', 'required');
                        input.placeholder = input.placeholder.replace('（可选）', '');
                    });
                    // 显示必填标记
                    if (douyinStoreNameRequired) douyinStoreNameRequired.style.display = 'inline';
                    if (douyinStoreIdRequired) douyinStoreIdRequired.style.display = 'inline';
                    logTestResult('✅ 抖店字段设为必填，显示红色*标记');
                } else {
                    // 设置为可选
                    douyinInputs.forEach(input => {
                        input.removeAttribute('required');
                        if (!input.placeholder.includes('（可选）')) {
                            input.placeholder = input.placeholder + '（可选）';
                        }
                    });
                    // 隐藏必填标记
                    if (douyinStoreNameRequired) douyinStoreNameRequired.style.display = 'none';
                    if (douyinStoreIdRequired) douyinStoreIdRequired.style.display = 'none';
                    logTestResult('✅ 抖店字段设为可选，隐藏红色*标记');
                }
            }
        }

        // 测试表单验证
        function testValidation() {
            const hasCustomerService = document.getElementById('has_customer_service').value === '1';
            const hasProductListing = document.getElementById('has_product_listing').value === '1';

            if (!hasCustomerService && !hasProductListing) {
                logTestResult('❌ 验证失败：必须选择至少一个卡密功能', 'error');
                return;
            }

            // 只有选择了微信小店功能时，才验证微信店铺信息
            if (hasCustomerService) {
                const wechatStoreName = document.querySelector('input[name="store_name"]');
                const wechatStoreId = document.querySelector('input[name="wechat_store_id"]');

                if (!wechatStoreName || !wechatStoreName.value.trim()) {
                    logTestResult('❌ 验证失败：选择微信小店功能时，微信店铺名称为必填项', 'error');
                    return;
                }

                if (!wechatStoreId || !wechatStoreId.value.trim()) {
                    logTestResult('❌ 验证失败：选择微信小店功能时，微信小店ID为必填项', 'error');
                    return;
                }

                logTestResult('✅ 验证通过：微信店铺信息完整', 'success');
            } else {
                logTestResult('✅ 验证通过：未选择微信小店功能，跳过微信店铺信息验证', 'success');
            }

            // 只有选择了抖店功能时，才验证抖店信息
            if (hasProductListing) {
                const douyinStoreName = document.querySelector('input[name="douyin_store_name"]');
                const douyinStoreId = document.querySelector('input[name="douyin_store_id"]');

                if (!douyinStoreName || !douyinStoreName.value.trim()) {
                    logTestResult('❌ 验证失败：选择抖店功能时，抖店店铺名称为必填项', 'error');
                    return;
                }

                if (!douyinStoreId || !douyinStoreId.value.trim()) {
                    logTestResult('❌ 验证失败：选择抖店功能时，抖店ID为必填项', 'error');
                    return;
                }

                logTestResult('✅ 验证通过：抖店信息完整', 'success');
            } else {
                logTestResult('✅ 验证通过：未选择抖店功能，跳过抖店信息验证', 'success');
            }
        }

        // 重置表单
        function resetForm() {
            document.getElementById('testForm').reset();
            document.querySelectorAll('.permission-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.getElementById('has_customer_service').value = '0';
            document.getElementById('has_product_listing').value = '0';
            handleStoreSection();
            logTestResult('🔄 表单已重置');
        }

        // 记录测试结果
        function logTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const resultItem = document.createElement('div');
            resultItem.className = `status ${type}`;
            resultItem.innerHTML = `[${timestamp}] ${message}`;
            resultsDiv.appendChild(resultItem);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            logTestResult('🚀 测试页面已加载，开始功能测试');
            handleStoreSection();
        });
    </script>
</body>
</html>
