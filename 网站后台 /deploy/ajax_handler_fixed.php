<?php
// 清理输出缓冲，确保JSON响应纯净
if (ob_get_level()) {
    ob_clean();
}

// 开始新的输出缓冲
ob_start();

session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 错误处理函数
function sendJsonResponse($success, $message, $data = null) {
    // 清理任何之前的输出
    if (ob_get_level()) {
        ob_clean();
    }
    
    $response = [
        'success' => $success,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendJsonResponse(false, '只支持POST请求');
}

// 检查用户是否已登录
if (!isset($_SESSION['admin_user_id'])) {
    sendJsonResponse(false, '用户未登录');
}

// 获取操作类型
$action = $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'toggle_script_status':
            handleToggleScriptStatus();
            break;
            
        case 'test_email':
            handleTestEmail();
            break;
            
        case 'trust_device':
            handleTrustDevice();
            break;
            
        case 'remove_device':
            handleRemoveDevice();
            break;
            
        case 'update_smtp':
            handleUpdateSmtp();
            break;
            
        case 'send_trust_device_code':
            handleSendTrustDeviceCode();
            break;
            
        case 'send_smtp_config_code':
            handleSendSmtpConfigCode();
            break;
            
        case 'test_connection':
            echo json_encode(['success' => true, 'message' => 'AJAX连接正常', 'timestamp' => date('Y-m-d H:i:s')]);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => '无效的操作类型']);
            break;
    }
} catch (Exception $e) {
    error_log('AJAX处理错误: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => '服务器内部错误: ' . $e->getMessage()]);
}

// 脚本状态切换处理
function handleToggleScriptStatus() {
    global $pdo;
    
    $script_id = intval($_POST['script_id'] ?? 0);
    $new_status = $_POST['new_status'] ?? '';
    
    if ($script_id <= 0) {
        echo json_encode(['success' => false, 'message' => '无效的脚本ID']);
        return;
    }
    
    if (!in_array($new_status, ['active', 'inactive'])) {
        echo json_encode(['success' => false, 'message' => '无效的状态值']);
        return;
    }
    
    try {
        // 检查status字段是否存在，如果不存在则添加
        $stmt_check = $pdo->query("SHOW COLUMNS FROM scripts LIKE 'status'");
        if ($stmt_check->rowCount() == 0) {
            $pdo->exec("ALTER TABLE scripts ADD COLUMN status ENUM('active','inactive') DEFAULT 'active'");
        }
        
        $stmt = $pdo->prepare("UPDATE scripts SET status = ? WHERE id = ?");
        if ($stmt->execute([$new_status, $script_id])) {
            echo json_encode(['success' => true, 'message' => '状态更新成功']);
        } else {
            $error_info = $stmt->errorInfo();
            echo json_encode(['success' => false, 'message' => '数据库更新失败: ' . ($error_info[2] ?? '未知错误')]);
        }
    } catch (Exception $e) {
        // 如果是字段相关错误，尝试直接更新
        if (strpos($e->getMessage(), 'Unknown column') !== false && strpos($e->getMessage(), 'status') !== false) {
            try {
                $pdo->exec("ALTER TABLE scripts ADD COLUMN status ENUM('active','inactive') DEFAULT 'active'");
                $stmt = $pdo->prepare("UPDATE scripts SET status = ? WHERE id = ?");
                if ($stmt->execute([$new_status, $script_id])) {
                    echo json_encode(['success' => true, 'message' => '状态更新成功']);
                } else {
                    $error_info = $stmt->errorInfo();
                    echo json_encode(['success' => false, 'message' => '数据库更新失败: ' . ($error_info[2] ?? '未知错误')]);
                }
            } catch (Exception $e2) {
                echo json_encode(['success' => false, 'message' => '更新失败：' . $e2->getMessage()]);
            }
        } else {
            // 如果字段已存在，直接更新
            try {
                $stmt = $pdo->prepare("UPDATE scripts SET status = ? WHERE id = ?");
                if ($stmt->execute([$new_status, $script_id])) {
                    echo json_encode(['success' => true, 'message' => '状态更新成功']);
                } else {
                    $error_info = $stmt->errorInfo();
                    echo json_encode(['success' => false, 'message' => '数据库更新失败: ' . ($error_info[2] ?? '未知错误')]);
                }
            } catch (Exception $e3) {
                echo json_encode(['success' => false, 'message' => '更新失败：' . $e3->getMessage()]);
            }
        }
    }
}

// 测试邮件发送处理
function handleTestEmail() {
    $test_email = $_POST['test_email'] ?? '';
    $smtp_host = $_POST['smtp_host'] ?? '';
    $smtp_port = $_POST['smtp_port'] ?? '';
    $smtp_username = $_POST['smtp_username'] ?? '';
    $smtp_password = $_POST['smtp_password'] ?? '';
    
    // 参数验证
    if (empty($test_email)) {
        echo json_encode(['success' => false, 'message' => '请输入测试邮箱地址']);
        return;
    }
    
    if (!filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => '邮箱格式不正确']);
        return;
    }
    
    if (empty($smtp_host) || empty($smtp_port) || empty($smtp_username) || empty($smtp_password)) {
        echo json_encode(['success' => false, 'message' => '请先完整配置SMTP设置']);
        return;
    }
    
    // 验证端口号
    $port = intval($smtp_port);
    if ($port <= 0 || $port > 65535) {
        echo json_encode(['success' => false, 'message' => 'SMTP端口号无效，请输入1-65535之间的数字']);
        return;
    }
    
    try {
        error_log("开始测试邮件发送 - 目标: $test_email, SMTP: $smtp_host:$smtp_port, 用户: $smtp_username");
        
        // 尝试发送测试邮件
        $result = sendTestEmailWithConfig($test_email, $smtp_host, $smtp_port, $smtp_username, $smtp_password);
        
        if ($result) {
            error_log("测试邮件发送成功: $test_email");
            echo json_encode([
                'success' => true, 
                'message' => '测试邮件发送成功！请检查邮箱（包括垃圾邮件文件夹）'
            ]);
        } else {
            error_log("测试邮件发送失败: $test_email");
            echo json_encode([
                'success' => false, 
                'message' => '测试邮件发送失败，请检查SMTP配置'
            ]);
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
        error_log("测试邮件发送异常: " . $error_message);
        
        echo json_encode([
            'success' => false, 
            'message' => '邮件发送失败：' . $error_message
        ]);
    }
}

// 信任设备处理
function handleTrustDevice() {
    global $pdo;
    
    $email_code = $_POST['email_code'] ?? '';
    $device_info = json_decode($_POST['device_info'] ?? '{}', true);
    
    if (empty($email_code)) {
        echo json_encode(['success' => false, 'message' => '验证码不能为空']);
        return;
    }
    
    if (!$device_info || !isset($device_info['fingerprint'])) {
        echo json_encode(['success' => false, 'message' => '设备信息无效']);
        return;
    }
    
    try {
        // 验证邮箱验证码（使用数据库方式）
        $stmt = $pdo->prepare("SELECT * FROM verification_codes WHERE 
            user_id = ? AND 
            code = ? AND 
            type = 'trust_device' AND 
            is_used = 0 AND 
            expires_at > NOW()
        ");
        $stmt->execute([$_SESSION['admin_user_id'], $email_code]);
        $code_record = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$code_record) {
            echo json_encode(['success' => false, 'message' => '验证码无效或已过期']);
            return;
        }
        
        // 标记验证码为已使用
        $stmt = $pdo->prepare("UPDATE verification_codes SET is_used = 1 WHERE id = ?");
        $stmt->execute([$code_record['id']]);
        
        // 确保trusted_devices表存在
        $pdo->exec("CREATE TABLE IF NOT EXISTS trusted_devices (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            device_name VARCHAR(255) NOT NULL,
            device_fingerprint VARCHAR(255) NOT NULL,
            user_agent TEXT,
            ip_address VARCHAR(45),
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY user_device (user_id, device_fingerprint),
            INDEX idx_user_id (user_id),
            INDEX idx_fingerprint (device_fingerprint)
        )");
        
        // 使用ON DUPLICATE KEY UPDATE处理重复设备
        $stmt = $pdo->prepare("
            INSERT INTO trusted_devices (user_id, device_name, device_fingerprint, user_agent, ip_address, created_at, is_active) 
            VALUES (?, ?, ?, ?, ?, NOW(), 1) 
            ON DUPLICATE KEY UPDATE 
                device_name = VALUES(device_name),
                user_agent = VALUES(user_agent),
                ip_address = VALUES(ip_address),
                is_active = 1,
                updated_at = NOW()
        ");
        
        $result = $stmt->execute([
            $_SESSION['admin_user_id'],
            $device_info['name'] ?? '未知设备',
            $device_info['fingerprint'],
            $device_info['user_agent'] ?? $_SERVER['HTTP_USER_AGENT'] ?? '',
            $_SERVER['REMOTE_ADDR'] ?? ''
        ]);
        
        if ($result) {
            // 检查是否是新插入还是更新
            if ($pdo->lastInsertId() > 0) {
                echo json_encode(['success' => true, 'message' => '设备已添加到信任列表']);
            } else {
                echo json_encode(['success' => true, 'message' => '设备信任状态已更新']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => '添加失败，请重试']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '操作失败：' . $e->getMessage()]);
    }
}

// 发送信任设备验证码 - 使用测试邮件的成功逻辑
function handleSendTrustDeviceCode() {
    global $pdo;
    
    try {
        error_log("=== 开始发送信任设备验证码 ===");
        error_log("用户ID: " . $_SESSION['admin_user_id']);
        
        // 获取用户邮箱
        $stmt = $pdo->prepare("SELECT email FROM admin_users WHERE id = ?");
        $stmt->execute([$_SESSION['admin_user_id']]);
        $user_email = $stmt->fetchColumn();
        
        error_log("查询到的用户邮箱: " . ($user_email ?: '空'));
        
        if (!$user_email) {
            error_log("❌ 用户邮箱未设置");
            echo json_encode(['success' => false, 'message' => '管理员邮箱未设置，请先在设置页面配置邮箱']);
            return;
        }
        
        // 检查系统设置
        $settings = getSystemSettings();
        error_log("SMTP配置检查:");
        error_log("- smtp_host: " . ($settings['smtp_host'] ?? 'null'));
        error_log("- smtp_port: " . ($settings['smtp_port'] ?? 'null'));
        error_log("- smtp_username: " . ($settings['smtp_username'] ?? 'null'));
        error_log("- smtp_password: " . (!empty($settings['smtp_password']) ? '***已设置***' : 'null'));
        
        if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
            error_log("❌ SMTP配置不完整");
            echo json_encode(['success' => false, 'message' => 'SMTP邮箱未配置，请先在设置页面配置邮箱']);
            return;
        }
        
        // 生成验证码
        $verification_code = generateVerificationCode();
        error_log("生成验证码: " . $verification_code);
        
        // 确保验证码表存在
        $pdo->exec("CREATE TABLE IF NOT EXISTS verification_codes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            code VARCHAR(6) NOT NULL,
            type VARCHAR(50) NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            is_used TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_code (user_id, code),
            INDEX idx_expires (expires_at)
        )");
        
        // 清理过期验证码
        $cleanup_result = $pdo->exec("DELETE FROM verification_codes WHERE expires_at < NOW() OR is_used = 1");
        error_log("清理了 $cleanup_result 条过期验证码");
        
        // 保存验证码到数据库（20秒有效期）
        $stmt = $pdo->prepare("INSERT INTO verification_codes (user_id, code, type, expires_at) VALUES (?, ?, 'trust_device', DATE_ADD(NOW(), INTERVAL 20 SECOND))");
        $insert_result = $stmt->execute([$_SESSION['admin_user_id'], $verification_code]);
        error_log("验证码保存结果: " . ($insert_result ? '成功' : '失败'));
        
        if (!$insert_result) {
            error_log("❌ 验证码保存失败");
            echo json_encode(['success' => false, 'message' => '验证码生成失败，请重试']);
            return;
        }
        
        error_log("开始发送邮件到: " . $user_email);
        
        // 使用与测试邮件相同的发送方式 - 关键修复点！
        $email_result = sendVerificationCodeWithTestMethod(
            $user_email, 
            $verification_code,
            'trust_device',
            $settings['smtp_host'], 
            $settings['smtp_port'], 
            $settings['smtp_username'], 
            $settings['smtp_password']
        );
        error_log("邮件发送结果: " . ($email_result ? '成功' : '失败'));
        
        if ($email_result) {
            echo json_encode(['success' => true, 'message' => '验证码已发送到您的邮箱（有效期20秒）']);
        } else {
            echo json_encode(['success' => false, 'message' => '验证码发送失败，请检查邮箱配置或网络连接']);
        }
        
    } catch (Exception $e) {
        error_log("❌ 信任设备验证码发送异常: " . $e->getMessage());
        error_log("异常堆栈: " . $e->getTraceAsString());
        echo json_encode(['success' => false, 'message' => '系统错误：' . $e->getMessage()]);
    }
}

// 移除设备处理
function handleRemoveDevice() {
    global $pdo;
    
    $device_id = intval($_POST['device_id'] ?? 0);
    
    if ($device_id <= 0) {
        echo json_encode(['success' => false, 'message' => '无效的设备ID']);
        return;
    }
    
    try {
        // 只能删除自己的信任设备
        $stmt = $pdo->prepare("UPDATE trusted_devices SET is_active = 0 WHERE id = ? AND user_id = ?");
        $result = $stmt->execute([$device_id, $_SESSION['admin_user_id']]);
        
        if ($result && $stmt->rowCount() > 0) {
            echo json_encode(['success' => true, 'message' => '设备已移除']);
        } else {
            echo json_encode(['success' => false, 'message' => '设备不存在或无权限']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '操作失败：' . $e->getMessage()]);
    }
}

// 更新SMTP配置
function handleUpdateSmtp() {
    global $pdo;
    
    $host = $_POST['smtp_host'] ?? '';
    $port = $_POST['smtp_port'] ?? '';
    $username = $_POST['smtp_username'] ?? '';
    $password = $_POST['smtp_password'] ?? '';
    $from_email = $_POST['from_email'] ?? '';
    $from_name = $_POST['from_name'] ?? '';
    $verification_code = $_POST['verification_code'] ?? '';
    
    // 获取复选框状态
    $email_verification_enabled = $_POST['email_verification_enabled'] ?? '0';
    $login_notification_enabled = $_POST['login_notification_enabled'] ?? '0';
    
    error_log("SMTP配置更新请求: email_verification_enabled=$email_verification_enabled, login_notification_enabled=$login_notification_enabled");
    
    // 确保system_settings表存在
    $pdo->exec("CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT,
        description VARCHAR(255) DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    // 确保smtp_config表存在
    $pdo->exec("CREATE TABLE IF NOT EXISTS smtp_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        smtp_host VARCHAR(255) NOT NULL,
        smtp_port INT NOT NULL DEFAULT 465,
        smtp_username VARCHAR(255) NOT NULL,
        smtp_password VARCHAR(255) NOT NULL,
        from_email VARCHAR(255) DEFAULT NULL,
        from_name VARCHAR(255) DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY user_smtp (user_id),
        INDEX idx_user_id (user_id)
    )");
    
    // 检查是否有现有配置
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM smtp_config WHERE user_id = ?");
    $stmt->execute([$_SESSION['admin_user_id']]);
    $has_existing_config = $stmt->fetchColumn() > 0;
    
    // 如果有现有配置，必须验证邮箱验证码
    if ($has_existing_config) {
        if (empty($verification_code)) {
            echo json_encode(['success' => false, 'message' => '修改邮箱配置需要邮箱验证码']);
            return;
        }
        
        // 验证邮箱验证码
        $stmt = $pdo->prepare("SELECT * FROM verification_codes WHERE 
            user_id = ? AND 
            code = ? AND 
            type = 'smtp_config' AND 
            is_used = 0 AND 
            expires_at > NOW()
        ");
        $stmt->execute([$_SESSION['admin_user_id'], $verification_code]);
        $code_record = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$code_record) {
            echo json_encode(['success' => false, 'message' => '验证码无效或已过期']);
            return;
        }
        
        // 标记验证码为已使用
        $stmt = $pdo->prepare("UPDATE verification_codes SET is_used = 1 WHERE id = ?");
        $stmt->execute([$code_record['id']]);
    }
    
    try {
        // 开始事务
        $pdo->beginTransaction();
        
        // 1. 更新SMTP配置到smtp_config表
        $stmt = $pdo->prepare("
            INSERT INTO smtp_config (user_id, smtp_host, smtp_port, smtp_username, smtp_password, from_email, from_name, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ON DUPLICATE KEY UPDATE
                smtp_host = VALUES(smtp_host),
                smtp_port = VALUES(smtp_port),
                smtp_username = VALUES(smtp_username),
                smtp_password = VALUES(smtp_password),
                from_email = VALUES(from_email),
                from_name = VALUES(from_name),
                updated_at = NOW()
        ");
        
        $stmt->execute([$_SESSION['admin_user_id'], $host, $port, $username, $password, $from_email, $from_name]);
        
        // 2. 更新系统设置到system_settings表
        $system_settings = [
            'smtp_host' => $host,
            'smtp_port' => $port,
            'smtp_username' => $username,
            'smtp_password' => $password,
            'email_verification_enabled' => $email_verification_enabled,
            'login_notification_enabled' => $login_notification_enabled
        ];
        
        foreach ($system_settings as $key => $value) {
            $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = NOW()");
            $stmt->execute([$key, $value, $value]);
        }
        
        // 提交事务
        $pdo->commit();
        
        error_log("SMTP配置更新成功: email_verification_enabled=$email_verification_enabled, login_notification_enabled=$login_notification_enabled");
        
        echo json_encode([
            'success' => true, 
            'message' => $has_existing_config ? '邮箱配置更新成功' : '邮箱配置保存成功',
            'has_existing_config' => true,  // 保存后就有配置了
            'settings_updated' => [
                'email_verification_enabled' => $email_verification_enabled,
                'login_notification_enabled' => $login_notification_enabled
            ]
        ]);
        
    } catch (Exception $e) {
        // 回滚事务
        $pdo->rollBack();
        error_log("SMTP配置更新失败: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => '保存失败：' . $e->getMessage()]);
    }
}

// 发送SMTP配置验证码 - 使用测试邮件的成功逻辑
function handleSendSmtpConfigCode() {
    global $pdo;
    
    try {
        error_log("=== 开始发送SMTP配置验证码 ===");
        error_log("用户ID: " . $_SESSION['admin_user_id']);
        
        // 获取当前SMTP配置
        $settings = getSystemSettings();
        error_log("SMTP配置检查:");
        error_log("- smtp_host: " . ($settings['smtp_host'] ?? 'null'));
        error_log("- smtp_port: " . ($settings['smtp_port'] ?? 'null'));
        error_log("- smtp_username: " . ($settings['smtp_username'] ?? 'null'));
        error_log("- smtp_password: " . (!empty($settings['smtp_password']) ? '***已设置***' : 'null'));
        
        if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
            error_log("❌ SMTP配置不完整");
            echo json_encode(['success' => false, 'message' => '当前无SMTP配置或配置不完整，请先完整配置SMTP设置']);
            return;
        }
        
        // 获取用户邮箱
        $stmt = $pdo->prepare("SELECT email FROM admin_users WHERE id = ?");
        $stmt->execute([$_SESSION['admin_user_id']]);
        $user_email = $stmt->fetchColumn();
        
        error_log("查询到的用户邮箱: " . ($user_email ?: '空'));
        
        if (!$user_email) {
            error_log("❌ 用户邮箱未设置");
            echo json_encode(['success' => false, 'message' => '管理员邮箱未设置，请先设置邮箱']);
            return;
        }
        
        // 生成验证码
        $verification_code = generateVerificationCode();
        error_log("生成验证码: " . $verification_code);
        
        // 确保验证码表存在
        $pdo->exec("CREATE TABLE IF NOT EXISTS verification_codes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            code VARCHAR(6) NOT NULL,
            type VARCHAR(50) NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            is_used TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_code (user_id, code),
            INDEX idx_expires (expires_at)
        )");
        
        // 清理过期验证码
        $cleanup_result = $pdo->exec("DELETE FROM verification_codes WHERE expires_at < NOW() OR is_used = 1");
        error_log("清理了 $cleanup_result 条过期验证码");
        
        // 保存验证码到数据库（20秒有效期）
        $stmt = $pdo->prepare("INSERT INTO verification_codes (user_id, code, type, expires_at) VALUES (?, ?, 'smtp_config', DATE_ADD(NOW(), INTERVAL 20 SECOND))");
        $insert_result = $stmt->execute([$_SESSION['admin_user_id'], $verification_code]);
        error_log("验证码保存结果: " . ($insert_result ? '成功' : '失败'));
        
        if (!$insert_result) {
            error_log("❌ 验证码保存失败");
            echo json_encode(['success' => false, 'message' => '验证码生成失败，请重试']);
            return;
        }
        
        error_log("开始发送邮件到: " . $user_email);
        
        // 使用与测试邮件相同的发送方式 - 关键修复点！
        $email_result = sendVerificationCodeWithTestMethod(
            $user_email, 
            $verification_code,
            'smtp_config',
            $settings['smtp_host'], 
            $settings['smtp_port'], 
            $settings['smtp_username'], 
            $settings['smtp_password']
        );
        error_log("邮件发送结果: " . ($email_result ? '成功' : '失败'));
        
        if ($email_result) {
            echo json_encode(['success' => true, 'message' => '验证码已发送到您的邮箱（有效期20秒）']);
        } else {
            echo json_encode(['success' => false, 'message' => '验证码发送失败，请检查邮箱配置或网络连接']);
        }
        
    } catch (Exception $e) {
        error_log("❌ SMTP配置验证码发送异常: " . $e->getMessage());
        error_log("异常堆栈: " . $e->getTraceAsString());
        echo json_encode(['success' => false, 'message' => '系统错误：' . $e->getMessage()]);
    }
}

// 使用测试邮件的成功方法发送验证码 - 关键修复函数！
function sendVerificationCodeWithTestMethod($to_email, $verification_code, $type, $smtp_host, $smtp_port, $smtp_username, $smtp_password) {
    error_log("使用测试邮件方法发送验证码 - 目标邮箱: $to_email, 验证码: $verification_code, 类型: $type");
    
    // 智能检测PHPMailer路径
    $phpmailer_paths = [
        __DIR__ . '/../includes/PHPMailer/src/PHPMailer.php',
        __DIR__ . '/../includes/PHPMailer/PHPMailer.php'
    ];
    
    $phpmailer_found = false;
    foreach ($phpmailer_paths as $path) {
        if (file_exists($path)) {
            $base_dir = dirname($path);
            if (file_exists($base_dir . '/Exception.php')) {
                require_once $base_dir . '/Exception.php';
            }
            if (file_exists($base_dir . '/PHPMailer.php')) {
                require_once $base_dir . '/PHPMailer.php';
            }
            if (file_exists($base_dir . '/SMTP.php')) {
                require_once $base_dir . '/SMTP.php';
            }
            $phpmailer_found = true;
            error_log("PHPMailer库加载成功: $path");
            break;
        }
    }
    
    if (!$phpmailer_found) {
        error_log('PHPMailer库未找到');
        return false;
    }
    
    // 根据不同版本使用不同的类名
    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        $encryption_starttls = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $encryption_smtps = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        error_log("使用命名空间版本的PHPMailer");
    } elseif (class_exists('PHPMailer')) {
        $mail = new PHPMailer(true);
        $encryption_starttls = 'tls';
        $encryption_smtps = 'ssl';
        error_log("使用传统版本的PHPMailer");
    } else {
        error_log('PHPMailer类未找到');
        return false;
    }
    
    try {
        // 服务器设置
        $mail->isSMTP();
        $mail->Host = $smtp_host;
        $mail->SMTPAuth = true;
        $mail->Username = $smtp_username;
        $mail->Password = $smtp_password;
        
        // 根据端口和主机选择加密方式
        $port = intval($smtp_port);
        
        error_log("配置SMTP - 主机: $smtp_host, 端口: $port");
        
        if ($port == 465) {
            $mail->SMTPSecure = $encryption_smtps;
            error_log("使用SSL加密 (端口465)");
        } elseif ($port == 587) {
            $mail->SMTPSecure = $encryption_starttls;
            error_log("使用STARTTLS加密 (端口587)");
        } elseif ($port == 25) {
            // 25端口通常不使用加密
            $mail->SMTPSecure = false;
            $mail->SMTPAuth = true;
            error_log("使用无加密连接 (端口25)");
        } else {
            $mail->SMTPSecure = $encryption_starttls;
            error_log("使用STARTTLS加密 (其他端口)");
        }
        
        $mail->Port = $port;
        $mail->CharSet = 'UTF-8';
        $mail->Timeout = 30;
        $mail->SMTPDebug = 0; // 生产环境关闭调试
        
        // 发件人和收件人
        $mail->setFrom($smtp_username, '小梅花AI客服系统');
        $mail->addAddress($to_email);
        
        // 邮件内容
        $mail->isHTML(true);
        
        if ($type === 'trust_device') {
            $mail->Subject = '【小梅花AI客服系统】设备信任验证码';
            $icon = '🛡️';
            $color = '#4CAF50';
            $title = '设备信任验证';
        } else {
            $mail->Subject = '【小梅花AI客服系统】邮箱配置验证码';
            $icon = '⚙️';
            $color = '#3498db';
            $title = '邮箱配置验证';
        }
        
        $mail->Body = '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px; overflow: hidden;">
            <div style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(20px); padding: 40px; text-align: center;">
                <div style="background: linear-gradient(135deg, ' . $color . ', #c44569); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; font-size: 32px; color: white;">
                    ' . $icon . '
                </div>
                <h1 style="color: white; font-size: 24px; margin-bottom: 20px;">' . $title . '</h1>
                <p style="color: rgba(255, 255, 255, 0.9); font-size: 16px; line-height: 1.6; margin-bottom: 30px;">
                    您的验证码是：
                </p>
                <div style="background: rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; margin: 20px 0;">
                    <div style="font-size: 36px; font-weight: bold; color: white; letter-spacing: 8px; font-family: monospace;">' . htmlspecialchars($verification_code) . '</div>
                    <p style="color: rgba(255, 255, 255, 0.8); margin: 10px 0 0 0; font-size: 14px;">验证码有效期：20秒</p>
                </div>
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid rgba(255, 255, 255, 0.2);">
                    <p style="color: rgba(255, 255, 255, 0.7); font-size: 14px;">
                        此邮件由小梅花AI客服系统自动发送，请勿回复。<br>
                        发送时间: ' . date('Y-m-d H:i:s') . '
                    </p>
                </div>
            </div>
        </div>';
        
        $mail->AltBody = "【小梅花AI客服系统】" . $title . "验证码：" . $verification_code . "（有效期20秒）\n\n发送时间：" . date('Y-m-d H:i:s');
        
        error_log("开始发送邮件到: $to_email");
        $mail->send();
        error_log("验证码邮件发送成功: $to_email");
        return true;
    } catch (Exception $e) {
        $error_msg = $e->getMessage();
        error_log("验证码邮件发送失败 - 错误信息: " . $error_msg);
        error_log("验证码邮件发送失败 - PHPMailer错误: " . $mail->ErrorInfo);
        
        // 分析常见错误并提供解决建议
        if (strpos($error_msg, 'SMTP connect() failed') !== false) {
            error_log("SMTP连接失败 - 可能是主机或端口配置错误");
        } elseif (strpos($error_msg, 'SMTP AUTH') !== false || strpos($error_msg, 'Authentication') !== false) {
            error_log("SMTP认证失败 - 可能是用户名或密码错误");
        } elseif (strpos($error_msg, 'tls') !== false || strpos($error_msg, 'ssl') !== false) {
            error_log("SSL/TLS连接失败 - 可能是加密方式配置错误");
        }
        
        return false;
    }
}

/**
 * 处理POST后重定向，避免页面刷新时重复提交
 */
function handlePostRedirect($page, $success_message = '', $error_message = '') {
    // 将消息存储到session中
    if (!empty($success_message)) {
        $_SESSION['flash_success'] = $success_message;
    }
    if (!empty($error_message)) {
        $_SESSION['flash_error'] = $error_message;
    }
    
    // 重定向到指定页面
    $redirect_url = "../xuxuemei/index.php?page=" . urlencode($page);
    header("Location: " . $redirect_url);
    exit;
}

/**
 * 获取并清除flash消息
 */
function getFlashMessage() {
    $messages = [];
    
    if (isset($_SESSION['flash_success'])) {
        $messages['success'] = $_SESSION['flash_success'];
        unset($_SESSION['flash_success']);
    }
    
    if (isset($_SESSION['flash_error'])) {
        $messages['error'] = $_SESSION['flash_error'];
        unset($_SESSION['flash_error']);
    }
    
    return $messages;
}
?>