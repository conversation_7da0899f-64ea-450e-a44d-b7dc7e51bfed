<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';

// 检查登录状态
require_login();

$current_page = $_GET['page'] ?? 'dashboard';

// 获取用户信息
$stmt = $pdo->prepare("SELECT * FROM admin_users WHERE id = ?");
$stmt->execute([$_SESSION['admin_user_id']]);
$admin_user = $stmt->fetch();

if (!$admin_user) {
    logout();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小梅花AI客服系统 - 管理后台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/style.css?v=<?php echo time(); ?>">
    <!-- 强制刷新CSS缓存，确保最新样式生效 -->
</head>
<body>
    <!-- 主内容包装器 -->
    <div class="main-wrapper">
        <!-- 主头部 -->
        <header class="main-header">
            <div class="header-left">
                <h1 class="header-title">
                    <i class="fas fa-robot"></i>
                    小梅花AI客服系统
                </h1>
            </div>
            <div class="header-right">
                <div class="header-user">
                    <div class="user-info">
                        <span class="user-name">管理员</span>
                        <span class="user-role">系统管理员</span>
                    </div>
                    <i class="fas fa-user-circle user-avatar"></i>
                    
                    <div class="user-dropdown">
                        <a href="index.php?page=settings" class="dropdown-item">
                            <i class="fas fa-cog"></i>
                            <span>系统设置</span>
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="logout.php" class="dropdown-item logout-item" onclick="return confirm('确定要退出登录吗？')">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>退出登录</span>
                        </a>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <?php
            switch ($current_page) {
                case 'dashboard':
                    include 'templates/dashboard.php';
                    break;
                case 'keys':
                    include 'templates/keys.php';
                    break;
                case 'scripts':
                    include 'templates/scripts.php';
                    break;

                case 'users':
                    include 'templates/users.php';
                    break;
                case 'analytics':
                    include 'templates/analytics.php';
                    break;
                case 'settings':
                    include 'templates/settings.php';
                    break;
                default:
                    include 'templates/dashboard.php';
                    break;
            }
            ?>
        </main>
    </div>
    
    <!-- 侧边栏 -->
    <aside class="sidebar" id="sidebar">
        
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="?page=dashboard" class="nav-link <?php echo $current_page === 'dashboard' ? 'active' : ''; ?>" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span class="nav-text">总数据</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="?page=keys" class="nav-link <?php echo $current_page === 'keys' ? 'active' : ''; ?>" data-page="keys">
                        <i class="fas fa-key"></i>
                        <span class="nav-text">卡密管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="?page=scripts" class="nav-link <?php echo $current_page === 'scripts' ? 'active' : ''; ?>" data-page="scripts">
                        <i class="fas fa-code"></i>
                        <span class="nav-text">脚本管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="?page=users" class="nav-link <?php echo $current_page === 'users' ? 'active' : ''; ?>" data-page="users">
                        <i class="fas fa-users"></i>
                        <span class="nav-text">用户管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="?page=analytics" class="nav-link <?php echo $current_page === 'analytics' ? 'active' : ''; ?>" data-page="analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span class="nav-text">数据分析</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="?page=settings" class="nav-link <?php echo $current_page === 'settings' ? 'active' : ''; ?>" data-page="settings">
                        <i class="fas fa-cog"></i>
                        <span class="nav-text">系统设置</span>
                    </a>
                </li>
            </ul>
        </nav>
        
        <div class="sidebar-footer">
            <div class="version-info">
                <small>测试版本 v1.0</small>
            </div>
        </div>
    </aside>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，初始化二级菜单...');
            
            // 初始化二级菜单功能
            const hasSubmenuItems = document.querySelectorAll('.nav-item.has-submenu');
            console.log('找到二级菜单项数量:', hasSubmenuItems.length);
            
            hasSubmenuItems.forEach((item, index) => {
                const navLink = item.querySelector('.nav-link');
                const submenu = item.querySelector('.submenu');
                
                console.log(`二级菜单项 ${index + 1}:`, item);
                console.log('主链接:', navLink);
                console.log('子菜单:', submenu);
                
                if (navLink && submenu) {
                    navLink.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('点击了脚本管理菜单');
                        
                        // 切换当前菜单的展开状态
                        const isActive = item.classList.contains('active');
                        console.log('当前状态:', isActive ? '已展开' : '已收起');
                        
                        if (isActive) {
                            item.classList.remove('active');
                            console.log('收起菜单');
                        } else {
                            item.classList.add('active');
                            console.log('展开菜单');
                        }
                        
                        // 关闭其他展开的子菜单
                        hasSubmenuItems.forEach(otherItem => {
                            if (otherItem !== item) {
                                otherItem.classList.remove('active');
                            }
                        });
                    });
                }
            });
            
            // 不再自动展开二级菜单，让用户手动点击控制
            console.log('🎯 二级菜单需要用户手动点击展开');
            
            console.log('二级菜单初始化完成');
        });
        
        // 头部用户下拉菜单功能
        document.addEventListener('click', function(event) {
            const headerUser = document.querySelector('.header-user');
            const userDropdown = document.querySelector('.user-dropdown');
            
            if (headerUser && userDropdown) {
                if (headerUser.contains(event.target)) {
                    // 如果点击的是下拉菜单内的链接，不切换状态
                    if (!event.target.closest('.dropdown-item')) {
                        userDropdown.classList.toggle('active');
                    }
                } else {
                    userDropdown.classList.remove('active');
                }
            }
        });
    </script>
</body>
</html> 