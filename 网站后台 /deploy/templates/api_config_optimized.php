<?php
/**
 * API接口配置管理页面 - 服务器优化版
 * 基于成功的server_optimized_api.php版本
 * 保持原有界面设计，解决卡顿问题
 */

// 性能优化设置
error_reporting(0);
ini_set('display_errors', 0);
set_time_limit(30);

// 优化session处理
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 辅助函数
function getIntervalText($seconds) {
    if ($seconds < 3600) {
        return floor($seconds / 60) . '分钟';
    } elseif ($seconds < 86400) {
        return floor($seconds / 3600) . '小时';
    } elseif ($seconds < 2592000) {
        return floor($seconds / 86400) . '天';
    } else {
        return floor($seconds / 2592000) . '个月';
    }
}

// 数据库连接优化
function getOptimizedDB() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 5,
                PDO::ATTR_PERSISTENT => false
            ];
            
            // 智能路径检测
            $possible_paths = [
                __DIR__ . '/../../database.db',
                dirname(dirname(__DIR__)) . '/database.db',
                $_SERVER['DOCUMENT_ROOT'] . '/database.db'
            ];
            
            $db_path = null;
            foreach ($possible_paths as $path) {
                if (file_exists($path)) {
                    $db_path = $path;
                    break;
                }
            }
            
            if ($db_path) {
                $pdo = new PDO('sqlite:' . $db_path, null, null, $options);
            } else {
                throw new Exception('数据库文件不存在');
            }
        } catch (Exception $e) {
            $pdo = false;
        }
    }
    
    return $pdo;
}

// AJAX请求处理
if (isset($_POST['ajax']) && $_POST['ajax'] === '1') {
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');
    
    $response = ['success' => false, 'message' => '', 'data' => ''];
    
    try {
        $pdo = getOptimizedDB();
        if (!$pdo) {
            throw new Exception('数据库连接失败');
        }
        
        $action = $_POST['action'] ?? '';
        
        if ($action === 'generate_api_key') {
            $keyName = $_POST['key_name'] ?? 'API密钥 ' . date('Y-m-d H:i:s');
            $keyValue = bin2hex(random_bytes(32));
            $autoRefresh = isset($_POST['auto_refresh']);
            $refreshInterval = (int)($_POST['refresh_interval'] ?? 86400);
            
            // 确保表存在
            $pdo->exec("CREATE TABLE IF NOT EXISTS api_keys (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key_name TEXT NOT NULL,
                api_key TEXT NOT NULL,
                is_active INTEGER DEFAULT 1,
                created_by INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )");
            
            $pdo->exec("CREATE TABLE IF NOT EXISTS token_management (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                token_type TEXT NOT NULL,
                token_value TEXT NOT NULL,
                expires_at DATETIME,
                auto_refresh INTEGER DEFAULT 1,
                refresh_interval INTEGER DEFAULT 86400,
                is_active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )");
            
            // 保存到API密钥表
            $stmt = $pdo->prepare('INSERT INTO api_keys (key_name, api_key, is_active, created_by) VALUES (?, ?, 1, 1)');
            $stmt->execute([$keyName, $keyValue]);
            $apiKeyId = $pdo->lastInsertId();
            
            // 保存到令牌管理表
            $expiresAt = $autoRefresh ? date('Y-m-d H:i:s', time() + $refreshInterval) : null;
            $stmt = $pdo->prepare('INSERT INTO token_management (token_type, token_value, expires_at, auto_refresh, refresh_interval) VALUES (?, ?, ?, ?, ?)');
            $stmt->execute(['api_key', $keyValue, $expiresAt, $autoRefresh ? 1 : 0, $refreshInterval]);
            
            $response['success'] = true;
            $response['message'] = 'API密钥生成成功，刷新间隔设置为 ' . getIntervalText($refreshInterval);
            $response['data'] = [
                'id' => $apiKeyId,
                'name' => $keyName,
                'key' => substr($keyValue, 0, 16) . '...',
                'time' => date('Y-m-d H:i:s')
            ];
        }
        
        if ($action === 'generate_jwt_secret') {
            $secret = bin2hex(random_bytes(64));
            $autoRefresh = isset($_POST['auto_refresh']);
            $refreshInterval = (int)($_POST['refresh_interval'] ?? 604800);
            
            // 确保表存在
            $pdo->exec("CREATE TABLE IF NOT EXISTS token_management (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                token_type TEXT NOT NULL,
                token_value TEXT NOT NULL,
                expires_at DATETIME,
                auto_refresh INTEGER DEFAULT 1,
                refresh_interval INTEGER DEFAULT 604800,
                is_active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )");
            
            $expiresAt = $autoRefresh ? date('Y-m-d H:i:s', time() + $refreshInterval) : null;
            $stmt = $pdo->prepare('INSERT INTO token_management (token_type, token_value, expires_at, auto_refresh, refresh_interval) VALUES (?, ?, ?, ?, ?)');
            $stmt->execute(['jwt_secret', $secret, $expiresAt, $autoRefresh ? 1 : 0, $refreshInterval]);
            
            $response['success'] = true;
            $response['message'] = 'JWT密钥生成成功，刷新间隔设置为 ' . getIntervalText($refreshInterval);
            $response['data'] = [
                'id' => $pdo->lastInsertId(),
                'secret' => substr($secret, 0, 16) . '...',
                'time' => date('Y-m-d H:i:s')
            ];
        }
        
        if ($action === 'update_refresh_interval') {
            $tokenType = $_POST['token_type'] ?? '';
            $refreshInterval = (int)($_POST['refresh_interval'] ?? 0);
            
            if ($tokenType && $refreshInterval > 0) {
                $stmt = $pdo->prepare('UPDATE token_management SET refresh_interval = ?, expires_at = datetime("now", "+" || ? || " seconds") WHERE token_type = ? AND is_active = 1');
                $result = $stmt->execute([$refreshInterval, $refreshInterval, $tokenType]);
                
                if ($result && $stmt->rowCount() > 0) {
                    $intervalText = getIntervalText($refreshInterval);
                    $typeText = $tokenType === 'api_key' ? 'API密钥' : 'JWT令牌';
                    $response['success'] = true;
                    $response['message'] = $typeText . '刷新间隔已更新为 ' . $intervalText;
                } else {
                    $response['message'] = '刷新间隔更新失败';
                }
            } else {
                $response['message'] = '无效的参数';
            }
        }
        
        if ($action === 'get_active_tokens') {
            $stmt = $pdo->prepare('SELECT id, token_type, expires_at, auto_refresh, created_at FROM token_management WHERE is_active = 1 ORDER BY id DESC LIMIT 5');
            $stmt->execute();
            $tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 在PHP中处理状态
            foreach ($tokens as &$token) {
                if ($token['expires_at'] === null) {
                    $token['expires_in'] = '永不过期';
                    $token['status_color'] = 'success';
                } elseif (strtotime($token['expires_at']) > time()) {
                    $token['expires_in'] = '有效';
                    $token['status_color'] = 'success';
                } else {
                    $token['expires_in'] = '已过期';
                    $token['status_color'] = 'danger';
                }
            }
            
            $response['success'] = true;
            $response['data'] = $tokens;
        }
        
        if ($action === 'get_api_keys') {
            $stmt = $pdo->prepare('SELECT id, key_name, created_at FROM api_keys WHERE is_active = 1 ORDER BY id DESC LIMIT 5');
            $stmt->execute();
            $apiKeys = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $response['success'] = true;
            $response['data'] = $apiKeys;
        }
        
    } catch (Exception $e) {
        $response['message'] = '操作失败: ' . $e->getMessage();
    }
    
    echo json_encode($response);
    exit;
}

// 页面加载时不执行任何数据库查询，避免卡顿
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API接口配置</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.4);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin: 0;
        }
        
        .btn {
            width: 100%;
            padding: 14px;
            border: none;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            font-size: 15px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover:before {
            left: 100%;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #28a745, #20c997);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #007bff, #6610f2);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }
        
        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .loading {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 10px;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.3);
            color: #28a745;
        }
        
        .alert-error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.3);
            color: #dc3545;
        }
        
        .token-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .token-table th,
        .token-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .token-table th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: 600;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-success {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }
        
        .status-danger {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>

<div class="container">
    <div class="header">
        <h1><i class="fas fa-cogs"></i> API接口配置</h1>
        <p>管理API密钥、安全设置和系统配置 (高性能版本)</p>
    </div>

    <div id="message-container"></div>

    <div class="grid">
        <!-- API密钥管理 -->
        <div class="glass-card">
            <h3 style="margin-bottom: 15px;">
                <i class="fas fa-key"></i> API密钥管理
            </h3>
            <form id="api-form">
                <div class="form-group">
                    <label>密钥名称</label>
                    <input type="text" id="api-name" value="<?php echo 'API密钥 ' . date('Y-m-d H:i:s'); ?>" required>
                </div>
                
                <div class="checkbox-group">
                    <input type="checkbox" id="api-auto-refresh" checked>
                    <label for="api-auto-refresh">启用自动刷新</label>
                </div>
                
                <div class="form-group">
                    <label>刷新间隔</label>
                    <select id="api-refresh-interval">
                        <option value="3600">1小时</option>
                        <option value="86400" selected>1天</option>
                        <option value="604800">7天</option>
                        <option value="2592000">30天</option>
                    </select>
                </div>
                
                <button type="submit" class="btn btn-primary" id="api-btn">
                    <span class="btn-text"><i class="fas fa-plus"></i> 生成新密钥</span>
                    <div class="loading">
                        <div class="spinner"></div>
                    </div>
                </button>
            </form>
        </div>

        <!-- JWT令牌管理 -->
        <div class="glass-card">
            <h3 style="margin-bottom: 15px;">
                <i class="fas fa-shield-alt"></i> JWT令牌管理
            </h3>
            <form id="jwt-form">
                <div class="form-group">
                    <label>令牌名称</label>
                    <input type="text" value="<?php echo 'JWT令牌 ' . date('Y-m-d H:i:s'); ?>" readonly>
                </div>
                
                <div class="checkbox-group">
                    <input type="checkbox" id="jwt-auto-refresh" checked>
                    <label for="jwt-auto-refresh">启用自动刷新</label>
                </div>
                
                <div class="form-group">
                    <label>刷新间隔</label>
                    <select id="jwt-refresh-interval">
                        <option value="3600">1小时</option>
                        <option value="86400">1天</option>
                        <option value="604800" selected>7天</option>
                        <option value="2592000">30天</option>
                    </select>
                </div>
                
                <button type="submit" class="btn btn-secondary" id="jwt-btn">
                    <span class="btn-text"><i class="fas fa-sync"></i> 生成新JWT密钥</span>
                    <div class="loading">
                        <div class="spinner"></div>
                    </div>
                </button>
            </form>
        </div>
    </div>

    <!-- 活动令牌状态 -->
    <div class="glass-card">
        <h3 style="margin-bottom: 15px;">
            <i class="fas fa-list-alt"></i> 活动令牌状态
        </h3>
        <div id="token-status">
            <p style="text-align: center; opacity: 0.7;">加载中...</p>
        </div>
        <button onclick="refreshTokenStatus()" class="btn btn-secondary" style="margin-top: 15px; max-width: 200px;">
            <i class="fas fa-refresh"></i> 刷新状态
        </button>
    </div>

    <div style="text-align: center; margin-top: 40px; opacity: 0.7;">
        <p><i class="fas fa-server"></i> 服务器优化版本 | <i class="fas fa-tachometer-alt"></i> 高性能 | <i class="fas fa-database"></i> 数据库同步</p>
    </div>
</div>

<script>
let isLoading = false;

// 显示消息
function showMessage(message, type = 'success') {
    const container = document.getElementById('message-container');
    const icon = type === 'success' ? '<i class="fas fa-check-circle"></i>' : '<i class="fas fa-exclamation-circle"></i>';
    const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
    
    container.innerHTML = `
        <div class="alert ${alertClass}">
            ${icon}
            <span>${message}</span>
        </div>
    `;
    
    setTimeout(() => {
        container.innerHTML = '';
    }, 4000);
}

// 设置按钮加载状态
function setLoading(buttonId, loading) {
    const btn = document.getElementById(buttonId);
    const text = btn.querySelector('.btn-text');
    const spinner = btn.querySelector('.loading');
    
    if (loading) {
        btn.disabled = true;
        text.style.opacity = '0';
        spinner.style.display = 'block';
        isLoading = true;
    } else {
        btn.disabled = false;
        text.style.opacity = '1';
        spinner.style.display = 'none';
        isLoading = false;
    }
}

// AJAX请求函数
async function makeRequest(data) {
    try {
        const response = await fetch(window.location.href, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({...data, ajax: '1'})
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        throw new Error('网络请求失败: ' + error.message);
    }
}

// API密钥生成
document.getElementById('api-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (isLoading) return;
    
    setLoading('api-btn', true);
    
    try {
        const keyName = document.getElementById('api-name').value.trim();
        const autoRefresh = document.getElementById('api-auto-refresh').checked;
        const refreshInterval = document.getElementById('api-refresh-interval').value;
        
        if (!keyName) {
            throw new Error('请输入密钥名称');
        }
        
        const response = await makeRequest({
            action: 'generate_api_key',
            key_name: keyName,
            auto_refresh: autoRefresh ? '1' : '',
            refresh_interval: refreshInterval
        });
        
        if (response.success) {
            showMessage('🎉 ' + response.message, 'success');
            // 更新密钥名称
            document.getElementById('api-name').value = 'API密钥 ' + new Date().toLocaleString('zh-CN');
            // 刷新令牌状态
            refreshTokenStatus();
        } else {
            showMessage(response.message, 'error');
        }
    } catch (error) {
        showMessage(error.message, 'error');
    }
    
    setLoading('api-btn', false);
});

// JWT令牌生成
document.getElementById('jwt-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (isLoading) return;
    
    setLoading('jwt-btn', true);
    
    try {
        const autoRefresh = document.getElementById('jwt-auto-refresh').checked;
        const refreshInterval = document.getElementById('jwt-refresh-interval').value;
        
        const response = await makeRequest({
            action: 'generate_jwt_secret',
            auto_refresh: autoRefresh ? '1' : '',
            refresh_interval: refreshInterval
        });
        
        if (response.success) {
            showMessage('🎉 ' + response.message, 'success');
            refreshTokenStatus();
        } else {
            showMessage(response.message, 'error');
        }
    } catch (error) {
        showMessage(error.message, 'error');
    }
    
    setLoading('jwt-btn', false);
});

// 刷新令牌状态
async function refreshTokenStatus() {
    try {
        const response = await makeRequest({action: 'get_active_tokens'});
        if (response.success) {
            updateTokenStatus(response.data);
        }
    } catch (error) {
        console.error('刷新状态失败:', error);
    }
}

// 更新令牌状态显示
function updateTokenStatus(tokens) {
    const container = document.getElementById('token-status');
    
    if (!tokens || tokens.length === 0) {
        container.innerHTML = '<p style="text-align: center; opacity: 0.7;">暂无活动令牌</p>';
        return;
    }
    
    let html = `
        <table class="token-table">
            <thead>
                <tr>
                    <th>类型</th>
                    <th>状态</th>
                    <th>过期时间</th>
                    <th>自动刷新</th>
                    <th>创建时间</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    tokens.forEach(token => {
        const typeName = token.token_type === 'api_key' ? 'API密钥' : 'JWT密钥';
        const autoRefreshText = token.auto_refresh ? '启用' : '禁用';
        const statusClass = token.status_color === 'success' ? 'status-success' : 'status-danger';
        const createdAt = new Date(token.created_at).toLocaleString('zh-CN');
        
        html += `
            <tr>
                <td>${typeName}</td>
                <td><span class="status-badge ${statusClass}">${token.expires_in}</span></td>
                <td>${token.expires_at || '永不过期'}</td>
                <td>${autoRefreshText}</td>
                <td>${createdAt}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table>';
    container.innerHTML = html;
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 API配置页面已加载');
    
    // 3秒后自动刷新令牌状态
    setTimeout(() => {
        refreshTokenStatus();
    }, 2000);
});

// 刷新间隔选择框变化事件
document.getElementById('api-refresh-interval').addEventListener('change', async function() {
    const refreshInterval = this.value;
    try {
        const response = await makeRequest({
            action: 'update_refresh_interval',
            token_type: 'api_key',
            refresh_interval: refreshInterval
        });
        
        if (response.success) {
            showMessage('✅ ' + response.message, 'success');
        } else {
            showMessage('❌ ' + response.message, 'error');
        }
    } catch (error) {
        showMessage('❌ 更新失败: ' + error.message, 'error');
    }
});

document.getElementById('jwt-refresh-interval').addEventListener('change', async function() {
    const refreshInterval = this.value;
    try {
        const response = await makeRequest({
            action: 'update_refresh_interval',
            token_type: 'jwt_secret',
            refresh_interval: refreshInterval
        });
        
        if (response.success) {
            showMessage('✅ ' + response.message, 'success');
        } else {
            showMessage('❌ ' + response.message, 'error');
        }
    } catch (error) {
        showMessage('❌ 更新失败: ' + error.message, 'error');
    }
});
</script>

</body>
</html> 