<?php
// 线上服务器优化版本 - 解决卡顿问题
error_reporting(0);
ini_set('display_errors', 0);

// 设置超时时间
set_time_limit(30);
ini_set('max_execution_time', 30);

// 优化session处理
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 数据库连接优化
function getOptimizedConnection() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            // 尝试连接数据库，使用超时设置
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 5,
                PDO::ATTR_PERSISTENT => false
            ];
            
            // 检查是否存在数据库配置
            $dbPath = __DIR__ . '/../../database.db';
            if (file_exists($dbPath)) {
                $pdo = new PDO('sqlite:' . $dbPath, null, null, $options);
            } else {
                throw new Exception('数据库文件不存在');
            }
        } catch (Exception $e) {
            // 数据库连接失败时的处理
            $pdo = false;
        }
    }
    
    return $pdo;
}

// AJAX请求处理
if (isset($_POST['ajax']) && $_POST['ajax'] === '1') {
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');
    
    $response = ['success' => false, 'message' => '', 'data' => ''];
    
    try {
        $pdo = getOptimizedConnection();
        if (!$pdo) {
            throw new Exception('数据库连接失败');
        }
        
        $action = $_POST['action'] ?? '';
        
        if ($action === 'generate_api_key') {
            $keyName = $_POST['key_name'] ?? 'API密钥-' . date('md-Hi');
            $keyValue = bin2hex(random_bytes(32));
            
            // 检查表是否存在
            $tableCheck = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='api_keys'");
            if (!$tableCheck->fetch()) {
                // 创建表
                $pdo->exec("
                    CREATE TABLE api_keys (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        key_name TEXT NOT NULL,
                        api_key TEXT NOT NULL,
                        is_active INTEGER DEFAULT 1,
                        created_by INTEGER DEFAULT 1,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ");
            }
            
            $stmt = $pdo->prepare('INSERT INTO api_keys (key_name, api_key, is_active, created_by) VALUES (?, ?, 1, 1)');
            if ($stmt->execute([$keyName, $keyValue])) {
                $response['success'] = true;
                $response['message'] = 'API密钥生成成功';
                $response['data'] = [
                    'id' => $pdo->lastInsertId(),
                    'name' => $keyName,
                    'key' => substr($keyValue, 0, 16) . '...',
                    'time' => date('Y-m-d H:i:s')
                ];
            } else {
                $response['message'] = 'API密钥生成失败';
            }
        }
        
        if ($action === 'generate_jwt_secret') {
            $secret = bin2hex(random_bytes(64));
            
            // 检查表是否存在
            $tableCheck = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='token_management'");
            if (!$tableCheck->fetch()) {
                // 创建表
                $pdo->exec("
                    CREATE TABLE token_management (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        token_type TEXT NOT NULL,
                        token_value TEXT NOT NULL,
                        is_active INTEGER DEFAULT 1,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ");
            }
            
            $stmt = $pdo->prepare('INSERT INTO token_management (token_type, token_value, is_active) VALUES (?, ?, 1)');
            if ($stmt->execute(['jwt_secret', $secret])) {
                $response['success'] = true;
                $response['message'] = 'JWT密钥生成成功';
                $response['data'] = [
                    'id' => $pdo->lastInsertId(),
                    'secret' => substr($secret, 0, 16) . '...',
                    'time' => date('Y-m-d H:i:s')
                ];
            } else {
                $response['message'] = 'JWT密钥生成失败';
            }
        }
        
        if ($action === 'get_keys') {
            $apiKeys = [];
            $tokens = [];
            
            // 检查表是否存在并获取数据
            $tableCheck = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='api_keys'");
            if ($tableCheck->fetch()) {
                $stmt = $pdo->prepare('SELECT id, key_name, created_at FROM api_keys ORDER BY id DESC LIMIT 5');
                $stmt->execute();
                $apiKeys = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
            
            $tableCheck = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='token_management'");
            if ($tableCheck->fetch()) {
                $stmt = $pdo->prepare("SELECT id, created_at FROM token_management WHERE token_type = 'jwt_secret' ORDER BY id DESC LIMIT 5");
                $stmt->execute();
                $tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
            
            $response['success'] = true;
            $response['data'] = [
                'api_keys' => $apiKeys,
                'tokens' => $tokens
            ];
        }
        
        if ($action === 'health_check') {
            $response['success'] = true;
            $response['message'] = '系统运行正常';
            $response['data'] = [
                'php_version' => PHP_VERSION,
                'memory_usage' => memory_get_usage(true),
                'database_connected' => $pdo ? true : false
            ];
        }
        
    } catch (Exception $e) {
        $response['message'] = '操作失败: ' . $e->getMessage();
    }
    
    echo json_encode($response);
    exit;
}

// 页面加载时不执行任何数据库查询，避免卡顿
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API配置 - 服务器优化版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .status-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h3 {
            margin-bottom: 20px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.4);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .btn {
            width: 100%;
            padding: 14px;
            border: none;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            font-size: 15px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover:before {
            left: 100%;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #28a745, #20c997);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #007bff, #6610f2);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }
        
        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .loading {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 10px;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.3);
            color: #28a745;
        }
        
        .alert-error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.3);
            color: #dc3545;
        }
        
        .results-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 20px;
        }
        
        .key-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .key-item {
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #28a745;
            transition: all 0.3s ease;
        }
        
        .key-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }
        
        .key-item strong {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .key-item small {
            opacity: 0.7;
            font-size: 12px;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            opacity: 0.7;
        }
        
        .footer a {
            color: white;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>

<div class="container">
    <div class="header">
        <h1>🚀 API配置中心</h1>
        <p>服务器优化版 - 专为线上环境设计</p>
    </div>

    <div class="status-bar">
        <div class="status-item">
            <div class="status-dot"></div>
            <span>系统运行中</span>
        </div>
        <div class="status-item">
            <span id="server-time"><?php echo date('Y-m-d H:i:s'); ?></span>
        </div>
        <div class="status-item">
            <button onclick="healthCheck()" style="background:none;border:1px solid rgba(255,255,255,0.3);color:white;padding:5px 10px;border-radius:5px;cursor:pointer;">
                健康检查
            </button>
        </div>
    </div>

    <div id="message-container"></div>

    <div class="grid">
        <!-- API密钥生成 -->
        <div class="card">
            <h3>
                <span>🔑</span>
                API密钥管理
            </h3>
            <form id="api-form">
                <div class="form-group">
                    <label>密钥名称</label>
                    <input type="text" id="api-name" value="<?php echo 'API密钥-' . date('md-Hi'); ?>" required>
                </div>
                <button type="submit" class="btn btn-primary" id="api-btn">
                    <span class="btn-text">生成API密钥</span>
                    <div class="loading">
                        <div class="spinner"></div>
                    </div>
                </button>
            </form>
        </div>

        <!-- JWT令牌生成 -->
        <div class="card">
            <h3>
                <span>🛡️</span>
                JWT令牌管理
            </h3>
            <form id="jwt-form">
                <div class="form-group">
                    <label>令牌描述</label>
                    <input type="text" value="<?php echo 'JWT令牌-' . date('md-Hi'); ?>" readonly>
                </div>
                <button type="submit" class="btn btn-secondary" id="jwt-btn">
                    <span class="btn-text">生成JWT令牌</span>
                    <div class="loading">
                        <div class="spinner"></div>
                    </div>
                </button>
            </form>
        </div>
    </div>

    <!-- 结果显示区域 -->
    <div class="results-card" id="results-card" style="display: none;">
        <h3 style="margin-bottom: 20px;">📋 生成记录</h3>
        <div id="key-lists">
            <p style="text-align: center; opacity: 0.7;">暂无记录</p>
        </div>
        <button onclick="refreshKeyList()" class="btn btn-secondary" style="margin-top: 15px; max-width: 200px;">
            刷新列表
        </button>
    </div>

    <div class="footer">
        <p>🌟 专为 <a href="https://xiaomeihuakefu.cn" target="_blank">xiaomeihuakefu.cn</a> 优化</p>
        <p>⚡ 异步处理 | 🚀 无卡顿 | 💾 数据库同步</p>
    </div>
</div>

<script>
// 全局变量
let isLoading = false;

// 显示消息
function showMessage(message, type = 'success') {
    const container = document.getElementById('message-container');
    const icon = type === 'success' ? '✅' : '❌';
    const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
    
    container.innerHTML = `
        <div class="alert ${alertClass}">
            <span>${icon}</span>
            <span>${message}</span>
        </div>
    `;
    
    setTimeout(() => {
        container.innerHTML = '';
    }, 4000);
}

// 设置按钮加载状态
function setLoading(buttonId, loading) {
    const btn = document.getElementById(buttonId);
    const text = btn.querySelector('.btn-text');
    const spinner = btn.querySelector('.loading');
    
    if (loading) {
        btn.disabled = true;
        text.style.opacity = '0';
        spinner.style.display = 'block';
        isLoading = true;
    } else {
        btn.disabled = false;
        text.style.opacity = '1';
        spinner.style.display = 'none';
        isLoading = false;
    }
}

// AJAX请求函数
async function makeRequest(data) {
    try {
        const response = await fetch(window.location.href, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({...data, ajax: '1'})
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        throw new Error('网络请求失败: ' + error.message);
    }
}

// API密钥生成
document.getElementById('api-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (isLoading) return;
    
    setLoading('api-btn', true);
    
    try {
        const keyName = document.getElementById('api-name').value.trim();
        if (!keyName) {
            throw new Error('请输入密钥名称');
        }
        
        const response = await makeRequest({
            action: 'generate_api_key',
            key_name: keyName
        });
        
        if (response.success) {
            showMessage('🎉 ' + response.message, 'success');
            // 更新密钥名称
            document.getElementById('api-name').value = 'API密钥-' + new Date().toLocaleString('zh-CN', {
                month: '2-digit', 
                day: '2-digit', 
                hour: '2-digit', 
                minute: '2-digit'
            }).replace(/[\/\s:]/g, '');
            
            // 显示结果
            showResults();
            refreshKeyList();
        } else {
            showMessage(response.message, 'error');
        }
    } catch (error) {
        showMessage(error.message, 'error');
    }
    
    setLoading('api-btn', false);
});

// JWT令牌生成
document.getElementById('jwt-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (isLoading) return;
    
    setLoading('jwt-btn', true);
    
    try {
        const response = await makeRequest({
            action: 'generate_jwt_secret'
        });
        
        if (response.success) {
            showMessage('🎉 ' + response.message, 'success');
            showResults();
            refreshKeyList();
        } else {
            showMessage(response.message, 'error');
        }
    } catch (error) {
        showMessage(error.message, 'error');
    }
    
    setLoading('jwt-btn', false);
});

// 显示结果区域
function showResults() {
    const resultsCard = document.getElementById('results-card');
    resultsCard.style.display = 'block';
    resultsCard.scrollIntoView({ behavior: 'smooth' });
}

// 刷新密钥列表
async function refreshKeyList() {
    try {
        const response = await makeRequest({action: 'get_keys'});
        if (response.success) {
            updateKeyList(response.data);
        }
    } catch (error) {
        console.error('刷新列表失败:', error);
    }
}

// 更新密钥列表显示
function updateKeyList(data) {
    const container = document.getElementById('key-lists');
    let html = '';
    
    if (data.api_keys && data.api_keys.length > 0) {
        html += '<h4 style="margin-bottom: 15px; color: #28a745;">🔑 API密钥 (' + data.api_keys.length + ')</h4>';
        data.api_keys.forEach(key => {
            const time = new Date(key.created_at).toLocaleString('zh-CN');
            html += `
                <div class="key-item">
                    <strong>${key.key_name}</strong>
                    <small>创建时间: ${time}</small>
                </div>
            `;
        });
    }
    
    if (data.tokens && data.tokens.length > 0) {
        html += '<h4 style="margin: 20px 0 15px 0; color: #007bff;">🛡️ JWT令牌 (' + data.tokens.length + ')</h4>';
        data.tokens.forEach(token => {
            const time = new Date(token.created_at).toLocaleString('zh-CN');
            html += `
                <div class="key-item" style="border-left-color: #007bff;">
                    <strong>JWT密钥</strong>
                    <small>创建时间: ${time}</small>
                </div>
            `;
        });
    }
    
    if (!data.api_keys?.length && !data.tokens?.length) {
        html = '<p style="text-align: center; opacity: 0.7;">暂无记录</p>';
    }
    
    container.innerHTML = html;
}

// 健康检查
async function healthCheck() {
    try {
        const response = await makeRequest({action: 'health_check'});
        if (response.success) {
            showMessage('✅ 系统健康检查通过', 'success');
        } else {
            showMessage('❌ 系统健康检查失败', 'error');
        }
    } catch (error) {
        showMessage('❌ 健康检查失败: ' + error.message, 'error');
    }
}

// 更新服务器时间
function updateServerTime() {
    const timeElement = document.getElementById('server-time');
    if (timeElement) {
        timeElement.textContent = new Date().toLocaleString('zh-CN');
    }
}

// 每秒更新时间
setInterval(updateServerTime, 1000);

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 API配置中心已加载');
    
    // 3秒后自动进行健康检查
    setTimeout(() => {
        healthCheck();
    }, 3000);
});
</script>

</body>
</html> 