<?php
/**
 * API接口配置管理页面 - 完全自包含版本
 * 版本: v3.1.0 - 终极修复版
 * 功能: 管理API密钥、JWT配置、安全设置
 * 特点: 不依赖任何外部文件，完全自包含
 */

// 开启错误显示（调试用）
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 辅助函数：将秒数转换为可读的时间间隔
function getIntervalText($seconds) {
    if ($seconds < 3600) {
        return floor($seconds / 60) . '分钟';
    } elseif ($seconds < 86400) {
        return floor($seconds / 3600) . '小时';
    } elseif ($seconds < 2592000) {
        return floor($seconds / 86400) . '天';
    } else {
        return floor($seconds / 2592000) . '个月';
    }
}

// 内联SimpleTokenManager类定义
class SimpleTokenManager {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->initializeTables();
    }
    
    /**
     * 初始化所有必要的表
     */
    public function initializeTables() {
        try {
            // 创建令牌管理表
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS token_management (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    token_type TEXT NOT NULL,
                    token_value TEXT NOT NULL,
                    expires_at DATETIME,
                    is_active BOOLEAN DEFAULT 1,
                    auto_refresh BOOLEAN DEFAULT 1,
                    refresh_interval INTEGER DEFAULT 86400,
                    created_at DATETIME DEFAULT (datetime('now')),
                    updated_at DATETIME DEFAULT (datetime('now'))
                )
            ");
            
            // 创建索引
            $this->pdo->exec("CREATE INDEX IF NOT EXISTS idx_type_active ON token_management (token_type, is_active)");
            $this->pdo->exec("CREATE INDEX IF NOT EXISTS idx_expires ON token_management (expires_at)");
            
            // 创建令牌同步日志表
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS token_sync_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    token_id INTEGER NOT NULL,
                    sync_type TEXT NOT NULL,
                    target_files TEXT,
                    status TEXT DEFAULT 'pending',
                    error_message TEXT,
                    created_at DATETIME DEFAULT (datetime('now'))
                )
            ");
            
            // 创建索引
            $this->pdo->exec("CREATE INDEX IF NOT EXISTS idx_token_id ON token_sync_log (token_id)");
            $this->pdo->exec("CREATE INDEX IF NOT EXISTS idx_status ON token_sync_log (status)");
            
            // 创建API密钥表
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS api_keys (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key_name TEXT NOT NULL,
                    api_key TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    created_by INTEGER DEFAULT 1,
                    created_at DATETIME DEFAULT (datetime('now')),
                    updated_at DATETIME DEFAULT (datetime('now'))
                )
            ");
            
            return true;
        } catch (Exception $e) {
            error_log("SimpleTokenManager表初始化失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 生成新的API密钥
     */
    public function generateApiKey($name = null, $autoRefresh = true, $refreshInterval = 86400) {
        try {
            $keyValue = bin2hex(random_bytes(32));
            $keyName = $name ?: 'Auto-Generated Key ' . date('Y-m-d H:i:s');
            
            // 保存到API密钥表
            $stmt = $this->pdo->prepare("
                INSERT INTO api_keys (key_name, api_key, is_active, created_by, created_at) 
                VALUES (?, ?, 1, ?, datetime('now'))
            ");
            $stmt->execute([$keyName, hash('sha256', $keyValue), $_SESSION['admin_user_id'] ?? 1]);
            $apiKeyId = $this->pdo->lastInsertId();
            
            // 保存到令牌管理表
            $expiresAt = $autoRefresh ? date('Y-m-d H:i:s', time() + $refreshInterval) : null;
            $stmt = $this->pdo->prepare("
                INSERT INTO token_management (token_type, token_value, expires_at, auto_refresh, refresh_interval) 
                VALUES ('api_key', ?, ?, ?, ?)
            ");
            $stmt->execute([$keyValue, $expiresAt, $autoRefresh, $refreshInterval]);
            $tokenId = $this->pdo->lastInsertId();
            
            return [
                'api_key_id' => $apiKeyId,
                'token_id' => $tokenId,
                'key_value' => $keyValue,
                'expires_at' => $expiresAt
            ];
        } catch (Exception $e) {
            error_log("生成API密钥失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 生成新的JWT密钥
     */
    public function generateJwtSecret($autoRefresh = true, $refreshInterval = 604800) {
        try {
            $secret = bin2hex(random_bytes(64));
            
            // 保存到令牌管理表
            $expiresAt = $autoRefresh ? date('Y-m-d H:i:s', time() + $refreshInterval) : null;
            $stmt = $this->pdo->prepare("
                INSERT INTO token_management (token_type, token_value, expires_at, auto_refresh, refresh_interval) 
                VALUES ('jwt_secret', ?, ?, ?, ?)
            ");
            $stmt->execute([$secret, $expiresAt, $autoRefresh, $refreshInterval]);
            $tokenId = $this->pdo->lastInsertId();
            
            return [
                'token_id' => $tokenId,
                'secret' => $secret,
                'expires_at' => $expiresAt
            ];
        } catch (Exception $e) {
            error_log("生成JWT密钥失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 自动刷新过期的令牌
     */
    public function autoRefreshTokens() {
        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM token_management 
                WHERE auto_refresh = 1 
                AND is_active = 1 
                AND expires_at <= datetime('now', '+1 hour')
            ");
            $stmt->execute();
            $expiring_tokens = $stmt->fetchAll();
            
            $refreshed = [];
            
            foreach ($expiring_tokens as $token) {
                try {
                    if ($token['token_type'] === 'api_key') {
                        $result = $this->refreshApiKey($token['id']);
                    } else {
                        $result = $this->refreshJwtSecret($token['id']);
                    }
                    
                    $refreshed[] = [
                        'id' => $token['id'],
                        'type' => $token['token_type'],
                        'status' => 'success',
                        'new_value' => $result['new_value']
                    ];
                    
                } catch (Exception $e) {
                    $refreshed[] = [
                        'id' => $token['id'],
                        'type' => $token['token_type'],
                        'status' => 'failed',
                        'error' => $e->getMessage()
                    ];
                }
            }
            
            return $refreshed;
        } catch (Exception $e) {
            error_log("自动刷新令牌失败: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 刷新API密钥
     */
    private function refreshApiKey($tokenId) {
        $stmt = $this->pdo->prepare("SELECT * FROM token_management WHERE id = ?");
        $stmt->execute([$tokenId]);
        $token = $stmt->fetch();
        
        if (!$token) {
            throw new Exception("Token not found");
        }
        
        // 生成新密钥
        $newKeyValue = bin2hex(random_bytes(32));
        $newExpiresAt = date('Y-m-d H:i:s', time() + $token['refresh_interval']);
        
        // 更新令牌管理表
        $stmt = $this->pdo->prepare("
            UPDATE token_management 
            SET token_value = ?, expires_at = ?, updated_at = datetime('now') 
            WHERE id = ?
        ");
        $stmt->execute([$newKeyValue, $newExpiresAt, $tokenId]);
        
        return ['new_value' => $newKeyValue, 'expires_at' => $newExpiresAt];
    }
    
    /**
     * 刷新JWT密钥
     */
    private function refreshJwtSecret($tokenId) {
        $stmt = $this->pdo->prepare("SELECT * FROM token_management WHERE id = ?");
        $stmt->execute([$tokenId]);
        $token = $stmt->fetch();
        
        if (!$token) {
            throw new Exception("Token not found");
        }
        
        // 生成新密钥
        $newSecret = bin2hex(random_bytes(64));
        $newExpiresAt = date('Y-m-d H:i:s', time() + $token['refresh_interval']);
        
        // 更新令牌管理表
        $stmt = $this->pdo->prepare("
            UPDATE token_management 
            SET token_value = ?, expires_at = ?, updated_at = datetime('now') 
            WHERE id = ?
        ");
        $stmt->execute([$newSecret, $newExpiresAt, $tokenId]);
        
        return ['new_value' => $newSecret, 'expires_at' => $newExpiresAt];
    }
    
    /**
     * 获取所有活动令牌信息
     */
    public function getActiveTokens() {
        try {
            $stmt = $this->pdo->prepare("
                SELECT tm.*, 
                       CASE 
                           WHEN tm.expires_at IS NULL THEN '永不过期'
                           WHEN tm.expires_at > datetime('now') THEN 
                               CAST((julianday(tm.expires_at) - julianday(datetime('now'))) * 86400 AS INTEGER) || '秒后过期'
                           ELSE '已过期'
                       END as expires_in,
                       CASE 
                           WHEN tm.expires_at IS NULL THEN 'success'
                           WHEN tm.expires_at > datetime('now', '+1 hour') THEN 'success'
                           WHEN tm.expires_at > datetime('now') THEN 'warning'
                           ELSE 'danger'
                       END as status_color
                FROM token_management tm 
                WHERE tm.is_active = 1 
                ORDER BY tm.token_type, tm.created_at DESC
            ");
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("获取活动令牌失败: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 获取同步日志
     */
    public function getSyncLogs($limit = 50) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT tsl.*, tm.token_type 
                FROM token_sync_log tsl 
                LEFT JOIN token_management tm ON tsl.token_id = tm.id 
                ORDER BY tsl.created_at DESC 
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("获取同步日志失败: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 更新令牌刷新间隔
     */
    public function updateRefreshInterval($tokenType, $refreshInterval) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE token_management 
                SET refresh_interval = ?, updated_at = datetime('now')
                WHERE token_type = ? AND is_active = 1
            ");
            $result = $stmt->execute([$refreshInterval, $tokenType]);
            
            if ($result && $stmt->rowCount() > 0) {
                // 记录同步日志
                $stmt = $this->pdo->prepare("
                    INSERT INTO token_sync_log (token_id, sync_type, target_files, status, created_at)
                    SELECT id, 'update_interval', '[]', 'success', datetime('now')
                    FROM token_management 
                    WHERE token_type = ? AND is_active = 1
                ");
                $stmt->execute([$tokenType]);
                
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            error_log("更新刷新间隔失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 扫描敏感信息（简化版）
     */
    public function scanSensitiveInfo() {
        // 返回空数组，避免复杂的文件扫描导致错误
        return [];
    }
}

// 初始化变量
$tokenManager = null;
$pdo = null;
$configError = null;
$success_message = null;
$error_message = null;

// 连接数据库 - 使用多种路径检测
try {
    // 智能路径检测 - 支持多种部署环境
    $possible_db_paths = [
        __DIR__ . '/../../database.db',           // 标准相对路径
        dirname(dirname(__DIR__)) . '/database.db', // 使用dirname方式
        $_SERVER['DOCUMENT_ROOT'] . '/database.db', // 网站根目录
        '/www/wwwroot/www.xiaomeihuakefu.cn/database.db' // 线上绝对路径
    ];
    
    $db_path = null;
    foreach ($possible_db_paths as $path) {
        if (file_exists($path)) {
            $db_path = $path;
            break;
        }
    }
    
    if (!$db_path) {
        throw new Exception("找不到数据库文件，检查路径: " . implode(', ', $possible_db_paths));
    }
    
    // 创建数据库连接
    $pdo = new PDO("sqlite:$db_path");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    if (isset($pdo) && $pdo !== null) {
        // 直接使用内联的SimpleTokenManager类
        $tokenManager = new SimpleTokenManager($pdo);
    } else {
        throw new Exception("数据库连接失败");
    }
} catch (Exception $e) {
    $configError = "系统初始化失败: " . $e->getMessage();
    error_log($configError);
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $tokenManager) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'generate_api_key':
            $keyName = $_POST['key_name'] ?? 'API Key ' . date('Y-m-d H:i:s');
            $autoRefresh = isset($_POST['auto_refresh']);
            $refreshInterval = (int)($_POST['refresh_interval'] ?? 86400);
            
            $result = $tokenManager->generateApiKey($keyName, $autoRefresh, $refreshInterval);
            if ($result) {
                $intervalText = getIntervalText($refreshInterval);
                $success_message = "新API密钥已生成，刷新间隔设置为 {$intervalText}";
            } else {
                $error_message = "API密钥生成失败";
            }
            break;
            
        case 'generate_jwt_secret':
            $autoRefresh = isset($_POST['auto_refresh']);
            $refreshInterval = (int)($_POST['refresh_interval'] ?? 604800);
            
            $result = $tokenManager->generateJwtSecret($autoRefresh, $refreshInterval);
            if ($result) {
                $intervalText = getIntervalText($refreshInterval);
                $success_message = "新JWT密钥已生成并同步，刷新间隔设置为 {$intervalText}";
            } else {
                $error_message = "JWT密钥生成失败";
            }
            break;
            
        case 'update_refresh_interval':
            $tokenType = $_POST['token_type'] ?? '';
            $refreshInterval = (int)($_POST['refresh_interval'] ?? 0);
            
            if ($tokenType && $refreshInterval > 0) {
                $result = $tokenManager->updateRefreshInterval($tokenType, $refreshInterval);
                if ($result) {
                    $intervalText = getIntervalText($refreshInterval);
                    $typeText = $tokenType === 'api_key' ? 'API密钥' : 'JWT令牌';
                    $success_message = "{$typeText}刷新间隔已更新为 {$intervalText}";
                } else {
                    $error_message = "刷新间隔更新失败";
                }
            } else {
                $error_message = "无效的参数";
            }
            break;
            
        case 'refresh_tokens':
            $refreshed = $tokenManager->autoRefreshTokens();
            $success_message = "已刷新 " . count($refreshed) . " 个令牌";
            break;
            
        case 'scan_sensitive':
            $scanResults = $tokenManager->scanSensitiveInfo();
            $success_message = "扫描完成，发现 " . count($scanResults) . " 个敏感信息";
            break;
    }
}

// 获取API密钥列表和令牌信息
$apiKeys = [];
$activeTokens = [];
$syncLogs = [];
$scanResults = [];

if ($pdo && $tokenManager) {
    try {
        // 获取API密钥列表
        $stmt = $pdo->prepare("SELECT * FROM api_keys ORDER BY created_at DESC");
        $stmt->execute();
        $apiKeys = $stmt->fetchAll();
        
        // 获取活动令牌
        $activeTokens = $tokenManager->getActiveTokens();
        
        // 获取同步日志
        $syncLogs = $tokenManager->getSyncLogs(20);
        
        // 获取扫描结果
        $scanResults = $tokenManager->scanSensitiveInfo();
        
    } catch (Exception $e) {
        error_log("获取数据失败: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API接口配置 - 自包含版本</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); margin: 0; padding: 20px; font-family: Arial, sans-serif; color: white;">

<div class="api-config-container" style="max-width: 1200px; margin: 0 auto;">
    <div class="page-header" style="text-align: center; margin-bottom: 30px;">
        <h2 style="font-size: 28px; margin-bottom: 10px;"><i class="fas fa-cogs"></i> API接口配置</h2>
        <p style="color: rgba(255, 255, 255, 0.7); font-size: 16px;">管理API密钥、安全设置和系统配置 (自包含版本)</p>
    </div>

    <?php if (isset($success_message)): ?>
    <div style="background: rgba(40, 167, 69, 0.1); border: 1px solid rgba(40, 167, 69, 0.3); color: #28a745; padding: 15px; border-radius: 8px; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
        <i class="fas fa-check-circle"></i>
        <?php echo htmlspecialchars($success_message); ?>
    </div>
    <?php endif; ?>
    
    <?php if (isset($error_message)): ?>
    <div style="background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); color: #dc3545; padding: 15px; border-radius: 8px; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
        <i class="fas fa-exclamation-circle"></i>
        <?php echo htmlspecialchars($error_message); ?>
    </div>
    <?php endif; ?>
    
    <?php if ($configError): ?>
    <div style="background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); color: #dc3545; padding: 15px; border-radius: 8px; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
        <i class="fas fa-exclamation-triangle"></i>
        <div>
            <strong>系统错误：</strong> <?php echo htmlspecialchars($configError); ?>
            <br><small>请检查数据库连接和文件权限。</small>
        </div>
    </div>
    <?php elseif (!$tokenManager): ?>
    <div style="background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); color: #dc3545; padding: 15px; border-radius: 8px; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
        <i class="fas fa-exclamation-triangle"></i>
        <div>
            <strong>令牌管理器未初始化</strong>
            <br><small>系统无法初始化令牌管理器，请检查数据库连接。</small>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($tokenManager): ?>
    <div style="background: rgba(255, 255, 255, 0.1); border-radius: 15px; backdrop-filter: blur(10px); overflow: hidden;">
        <!-- API密钥管理 -->
        <div style="padding: 30px;">
            <h3 style="color: white; margin-bottom: 20px; font-size: 18px; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-key"></i> API密钥管理
            </h3>
            
            <!-- 生成新密钥 -->
            <form method="post" style="max-width: 600px; background: rgba(255, 255, 255, 0.05); border-radius: 12px; padding: 25px; margin-bottom: 20px;">
                <input type="hidden" name="action" value="generate_api_key">
                
                <div style="margin-bottom: 20px;">
                    <label style="display: block; color: white; margin-bottom: 8px; font-weight: 500;">密钥名称</label>
                    <input type="text" name="key_name" value="<?php echo '系统密钥 ' . date('Y-m-d H:i:s'); ?>" required
                           style="width: 100%; padding: 12px 15px; background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 8px; color: white; font-size: 14px;">
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                    <div>
                        <label style="display: flex; align-items: center; cursor: pointer; color: white; font-size: 14px; font-weight: 500;">
                            <input type="checkbox" name="auto_refresh" checked style="margin-right: 10px;">
                            启用自动刷新
                        </label>
                    </div>
                    
                    <div>
                        <label style="display: block; color: white; margin-bottom: 8px; font-weight: 500;">刷新间隔</label>
                        <select name="refresh_interval" style="width: 100%; padding: 10px 15px; background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 8px; color: white; font-size: 14px;">
                            <option value="3600">1小时</option>
                            <option value="86400" selected>24小时</option>
                            <option value="604800">7天</option>
                            <option value="2592000">30天</option>
                        </select>
                    </div>
                </div>
                
                <div>
                    <button type="submit" style="padding: 12px 25px; background: linear-gradient(135deg, #28a745, #20c997); color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-plus"></i> 生成新密钥
                    </button>
                </div>
            </form>
            
            <!-- JWT令牌管理 -->
            <h3 style="color: white; margin-bottom: 20px; font-size: 18px; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-shield-alt"></i> JWT令牌管理
            </h3>
            
            <form method="post" style="max-width: 600px; background: rgba(255, 255, 255, 0.05); border-radius: 12px; padding: 25px; margin-bottom: 20px;">
                <input type="hidden" name="action" value="generate_jwt_secret">
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                    <div>
                        <label style="display: flex; align-items: center; cursor: pointer; color: white; font-size: 14px; font-weight: 500;">
                            <input type="checkbox" name="auto_refresh" checked style="margin-right: 10px;">
                            启用自动刷新
                        </label>
                    </div>
                    
                    <div>
                        <label style="display: block; color: white; margin-bottom: 8px; font-weight: 500;">刷新间隔</label>
                        <select name="refresh_interval" style="width: 100%; padding: 10px 15px; background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 8px; color: white; font-size: 14px;">
                            <option value="86400">24小时</option>
                            <option value="604800" selected>7天</option>
                            <option value="2592000">30天</option>
                            <option value="7776000">90天</option>
                        </select>
                    </div>
                </div>
                
                <div>
                    <button type="submit" style="padding: 12px 25px; background: linear-gradient(135deg, #007bff, #0056b3); color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-sync"></i> 生成新JWT密钥
                    </button>
                </div>
            </form>
            
            <!-- 活动令牌状态 -->
            <h3 style="color: white; margin-bottom: 20px; font-size: 18px; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-list"></i> 活动令牌状态
            </h3>
            
            <?php if (empty($activeTokens)): ?>
            <div style="text-align: center; padding: 40px; color: rgba(255, 255, 255, 0.6); background: rgba(255, 255, 255, 0.05); border-radius: 12px;">
                <i class="fas fa-clock" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                <p>暂无活动令牌</p>
            </div>
            <?php else: ?>
            <div style="background: rgba(255, 255, 255, 0.05); border-radius: 12px; overflow: hidden; margin-bottom: 20px;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: rgba(255, 255, 255, 0.05);">
                            <th style="padding: 12px; text-align: left; color: white; font-weight: 600;">类型</th>
                            <th style="padding: 12px; text-align: left; color: white; font-weight: 600;">状态</th>
                            <th style="padding: 12px; text-align: left; color: white; font-weight: 600;">过期时间</th>
                            <th style="padding: 12px; text-align: left; color: white; font-weight: 600;">自动刷新</th>
                            <th style="padding: 12px; text-align: left; color: white; font-weight: 600;">创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($activeTokens as $token): ?>
                        <tr style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <td style="padding: 12px; color: white;">
                                <?php echo $token['token_type'] === 'api_key' ? 'API密钥' : 'JWT密钥'; ?>
                            </td>
                            <td style="padding: 12px; color: white;">
                                <span style="padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; background: rgba(40, 167, 69, 0.2); color: #28a745;">
                                    <?php echo $token['expires_in']; ?>
                                </span>
                            </td>
                            <td style="padding: 12px; color: white;">
                                <?php echo $token['expires_at'] ? date('Y-m-d H:i:s', strtotime($token['expires_at'])) : '永不过期'; ?>
                            </td>
                            <td style="padding: 12px; color: white;">
                                <span style="color: <?php echo $token['auto_refresh'] ? '#28a745' : '#dc3545'; ?>;">
                                    <?php echo $token['auto_refresh'] ? '启用' : '禁用'; ?>
                                </span>
                            </td>
                            <td style="padding: 12px; color: white;">
                                <?php echo date('Y-m-d H:i:s', strtotime($token['created_at'])); ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
            
            <!-- 现有API密钥列表 -->
            <h3 style="color: white; margin-bottom: 20px; font-size: 18px; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-key"></i> 现有API密钥
            </h3>
            
            <?php if (empty($apiKeys)): ?>
            <div style="text-align: center; padding: 40px; color: rgba(255, 255, 255, 0.6); background: rgba(255, 255, 255, 0.05); border-radius: 12px;">
                <i class="fas fa-key" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                <p>暂无API密钥，请先生成一个</p>
            </div>
            <?php else: ?>
            <div style="background: rgba(255, 255, 255, 0.05); border-radius: 12px; overflow: hidden;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: rgba(255, 255, 255, 0.05);">
                            <th style="padding: 12px; text-align: left; color: white; font-weight: 600;">密钥名称</th>
                            <th style="padding: 12px; text-align: left; color: white; font-weight: 600;">创建时间</th>
                            <th style="padding: 12px; text-align: left; color: white; font-weight: 600;">状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($apiKeys as $key): ?>
                        <tr style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <td style="padding: 12px; color: white;"><?php echo htmlspecialchars($key['key_name']); ?></td>
                            <td style="padding: 12px; color: white;"><?php echo date('Y-m-d H:i', strtotime($key['created_at'])); ?></td>
                            <td style="padding: 12px; color: white;">
                                <span style="padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; background: rgba(40, 167, 69, 0.2); color: #28a745;">
                                    <?php echo $key['is_active'] ? '启用' : '禁用'; ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
            
            <div style="margin-top: 30px; text-align: center; font-size: 14px; opacity: 0.8;">
                <p>🎯 自包含版本 | ⚡ 零依赖运行 | 🔒 完全兼容</p>
                <p style="font-size: 12px;">版本: v3.1.0 - 终极修复版 | 修复时间: 2025-06-29</p>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

</body>
</html> 