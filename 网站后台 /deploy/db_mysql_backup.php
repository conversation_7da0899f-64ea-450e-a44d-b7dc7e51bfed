<?php
// 导入环境变量管理系统
require_once dirname(__DIR__) . '/api/env_manager.php';

// 初始化环境管理器并加载环境变量
$env_manager = EnvManager::getInstance();
$env_manager->load();

// 使用环境变量获取数据库配置
$db_config = $env_manager->getDatabaseConfig();

// === 数据库连接配置 ===
$db_host = $db_config['host'];
$db_user = $db_config['user'];
$db_pass = $db_config['pass'];
$db_name = $db_config['dbname'];
// ===========================

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    // 禁用预处理语句模拟，确保使用真正的预处理语句
    $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
    
    // 辅助函数 - 执行预处理查询
    function backup_db_query($sql, $params = []) {
        global $pdo;
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    }
    
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}
?> 