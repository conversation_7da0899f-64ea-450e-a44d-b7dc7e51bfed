<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI控制面板删除测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-result {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        .code-block {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 AI控制面板删除测试</h1>
            <p>验证AI知识库页面的控制面板是否已成功删除</p>
        </div>

        <div class="test-section">
            <h3>1. 控制面板元素检测</h3>
            <button onclick="testPanelElements()">检测控制面板元素</button>
            <div id="panel-test-result"></div>
        </div>

        <div class="test-section">
            <h3>2. AI助手对象检测</h3>
            <button onclick="testAIObject()">检测AI助手对象</button>
            <div id="ai-object-result"></div>
        </div>

        <div class="test-section">
            <h3>3. 浮动按钮检测</h3>
            <button onclick="testFloatingButton()">检测浮动按钮</button>
            <div id="floating-button-result"></div>
        </div>

        <div class="test-section">
            <h3>4. 脚本注入检测</h3>
            <button onclick="testScriptInjection()">检测脚本注入</button>
            <div id="script-injection-result"></div>
        </div>

        <div class="test-section">
            <h3>5. 模拟AI知识库页面加载</h3>
            <button onclick="simulateAIKnowledgePage()">模拟页面加载</button>
            <div id="simulation-result"></div>
        </div>

        <div class="test-section">
            <h3>6. 测试总结</h3>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="summary-result"></div>
        </div>
    </div>

    <script>
        // 测试控制面板元素
        function testPanelElements() {
            const resultDiv = document.getElementById('panel-test-result');
            let results = [];
            
            // 检查是否存在控制面板相关元素
            const panelElements = [
                'xiaomeihua-ai-panel',
                'xiaomeihua-ai-button'
            ];
            
            panelElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    results.push(`❌ 发现元素: ${id}`);
                } else {
                    results.push(`✅ 元素已删除: ${id}`);
                }
            });
            
            // 检查是否存在包含特定文本的元素
            const textChecks = [
                'AI助手已就绪',
                '小梅花AI助手',
                '开始对话',
                '设置'
            ];
            
            textChecks.forEach(text => {
                const elements = document.querySelectorAll('*');
                let found = false;
                elements.forEach(el => {
                    if (el.textContent && el.textContent.includes(text)) {
                        found = true;
                    }
                });
                
                if (found) {
                    results.push(`❌ 发现文本: "${text}"`);
                } else {
                    results.push(`✅ 文本已删除: "${text}"`);
                }
            });
            
            const allPassed = results.every(r => r.startsWith('✅'));
            resultDiv.innerHTML = `
                <div class="${allPassed ? 'success' : 'error'}">
                    ${allPassed ? '🎉 所有控制面板元素已成功删除！' : '⚠️ 仍有控制面板元素存在'}
                </div>
                <div class="code-block">${results.join('<br>')}</div>
            `;
        }
        
        // 测试AI助手对象
        function testAIObject() {
            const resultDiv = document.getElementById('ai-object-result');
            let results = [];
            
            // 检查全局AI对象
            if (window.xiaomeihuaAI) {
                results.push(`❌ 发现全局对象: xiaomeihuaAI`);
                
                // 检查对象的方法
                const methods = ['init', 'createFloatingButton', 'createPanel', 'togglePanel'];
                methods.forEach(method => {
                    if (window.xiaomeihuaAI[method]) {
                        results.push(`❌ 发现方法: xiaomeihuaAI.${method}`);
                    } else {
                        results.push(`✅ 方法已删除: xiaomeihuaAI.${method}`);
                    }
                });
            } else {
                results.push(`✅ 全局对象已删除: xiaomeihuaAI`);
            }
            
            const allPassed = results.every(r => r.startsWith('✅'));
            resultDiv.innerHTML = `
                <div class="${allPassed ? 'success' : 'error'}">
                    ${allPassed ? '🎉 AI助手对象已成功删除！' : '⚠️ 仍有AI助手对象存在'}
                </div>
                <div class="code-block">${results.join('<br>')}</div>
            `;
        }
        
        // 测试浮动按钮
        function testFloatingButton() {
            const resultDiv = document.getElementById('floating-button-result');
            let results = [];
            
            // 检查浮动按钮
            const floatingButtons = document.querySelectorAll('[id*="ai-button"], [class*="floating"], [style*="position: fixed"]');
            
            if (floatingButtons.length === 0) {
                results.push('✅ 未发现浮动按钮');
            } else {
                floatingButtons.forEach((btn, index) => {
                    results.push(`❌ 发现浮动按钮 ${index + 1}: ${btn.id || btn.className || '无标识'}`);
                });
            }
            
            // 检查是否有机器人emoji
            const robotEmojis = document.querySelectorAll('*');
            let robotFound = false;
            robotEmojis.forEach(el => {
                if (el.textContent && el.textContent.includes('🤖')) {
                    robotFound = true;
                }
            });
            
            if (robotFound) {
                results.push('❌ 发现机器人图标 🤖');
            } else {
                results.push('✅ 机器人图标已删除');
            }
            
            const allPassed = results.every(r => r.startsWith('✅'));
            resultDiv.innerHTML = `
                <div class="${allPassed ? 'success' : 'error'}">
                    ${allPassed ? '🎉 浮动按钮已成功删除！' : '⚠️ 仍有浮动按钮存在'}
                </div>
                <div class="code-block">${results.join('<br>')}</div>
            `;
        }
        
        // 测试脚本注入
        function testScriptInjection() {
            const resultDiv = document.getElementById('script-injection-result');
            let results = [];
            
            // 检查是否有相关的脚本标签
            const scripts = document.querySelectorAll('script');
            let foundControlPanelScript = false;
            
            scripts.forEach((script, index) => {
                if (script.textContent && 
                    (script.textContent.includes('createPanel') || 
                     script.textContent.includes('AI助手已就绪') ||
                     script.textContent.includes('xiaomeihuaAI'))) {
                    foundControlPanelScript = true;
                    results.push(`❌ 发现控制面板脚本 ${index + 1}`);
                }
            });
            
            if (!foundControlPanelScript) {
                results.push('✅ 未发现控制面板相关脚本');
            }
            
            // 检查控制台输出
            results.push('ℹ️ 请检查控制台是否有"AI助手控制面板已禁用"消息');
            
            const allPassed = !foundControlPanelScript;
            resultDiv.innerHTML = `
                <div class="${allPassed ? 'success' : 'error'}">
                    ${allPassed ? '🎉 控制面板脚本已成功删除！' : '⚠️ 仍有控制面板脚本存在'}
                </div>
                <div class="code-block">${results.join('<br>')}</div>
            `;
        }
        
        // 模拟AI知识库页面加载
        function simulateAIKnowledgePage() {
            const resultDiv = document.getElementById('simulation-result');
            
            // 创建一个iframe来加载AI知识库页面
            const iframe = document.createElement('iframe');
            iframe.src = 'ai-knowledge.html';
            iframe.style.cssText = 'width: 100%; height: 300px; border: 1px solid #ddd; border-radius: 8px;';
            
            iframe.onload = function() {
                setTimeout(() => {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        
                        // 检查iframe中是否有控制面板
                        const panelInIframe = iframeDoc.getElementById('xiaomeihua-ai-panel');
                        const buttonInIframe = iframeDoc.getElementById('xiaomeihua-ai-button');
                        
                        let results = [];
                        if (panelInIframe) {
                            results.push('❌ AI知识库页面中仍有控制面板');
                        } else {
                            results.push('✅ AI知识库页面中无控制面板');
                        }
                        
                        if (buttonInIframe) {
                            results.push('❌ AI知识库页面中仍有浮动按钮');
                        } else {
                            results.push('✅ AI知识库页面中无浮动按钮');
                        }
                        
                        const allPassed = results.every(r => r.startsWith('✅'));
                        resultDiv.innerHTML = `
                            <div class="${allPassed ? 'success' : 'error'}">
                                ${allPassed ? '🎉 AI知识库页面清理成功！' : '⚠️ AI知识库页面仍有问题'}
                            </div>
                            <div class="code-block">${results.join('<br>')}</div>
                            ${iframe.outerHTML}
                        `;
                    } catch (e) {
                        resultDiv.innerHTML = `
                            <div class="info">
                                ℹ️ 无法访问iframe内容（可能是跨域限制），但页面已加载
                            </div>
                            ${iframe.outerHTML}
                        `;
                    }
                }, 2000);
            };
            
            iframe.onerror = function() {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ 无法加载AI知识库页面
                    </div>
                `;
            };
            
            resultDiv.innerHTML = '<div class="info">⏳ 正在加载AI知识库页面...</div>';
            resultDiv.appendChild(iframe);
        }
        
        // 运行所有测试
        function runAllTests() {
            const resultDiv = document.getElementById('summary-result');
            resultDiv.innerHTML = '<div class="info">⏳ 正在运行所有测试...</div>';
            
            // 依次运行所有测试
            setTimeout(() => {
                testPanelElements();
                testAIObject();
                testFloatingButton();
                testScriptInjection();
                
                setTimeout(() => {
                    // 汇总结果
                    const allResults = [
                        document.getElementById('panel-test-result'),
                        document.getElementById('ai-object-result'),
                        document.getElementById('floating-button-result'),
                        document.getElementById('script-injection-result')
                    ];
                    
                    const successCount = allResults.filter(div => 
                        div.innerHTML.includes('success')
                    ).length;
                    
                    const totalTests = allResults.length;
                    const allPassed = successCount === totalTests;
                    
                    resultDiv.innerHTML = `
                        <div class="${allPassed ? 'success' : 'error'}">
                            ${allPassed ? '🎉 所有测试通过！' : `⚠️ ${successCount}/${totalTests} 测试通过`}
                        </div>
                        <div class="info">
                            <h4>测试总结：</h4>
                            <ul>
                                <li>控制面板元素检测: ${document.getElementById('panel-test-result').innerHTML.includes('success') ? '✅ 通过' : '❌ 失败'}</li>
                                <li>AI助手对象检测: ${document.getElementById('ai-object-result').innerHTML.includes('success') ? '✅ 通过' : '❌ 失败'}</li>
                                <li>浮动按钮检测: ${document.getElementById('floating-button-result').innerHTML.includes('success') ? '✅ 通过' : '❌ 失败'}</li>
                                <li>脚本注入检测: ${document.getElementById('script-injection-result').innerHTML.includes('success') ? '✅ 通过' : '❌ 失败'}</li>
                            </ul>
                            ${allPassed ? 
                                '<p><strong>🎊 恭喜！AI控制面板已成功删除，所有相关代码已清理完毕。</strong></p>' : 
                                '<p><strong>⚠️ 仍有部分控制面板代码需要清理，请检查失败的测试项目。</strong></p>'
                            }
                        </div>
                    `;
                }, 1000);
            }, 500);
        }
        
        // 页面加载完成后自动运行基础检测
        window.addEventListener('load', function() {
            console.log('🔧 AI控制面板删除测试页面已加载');
            console.log('请点击相应按钮进行测试，或点击"运行所有测试"进行全面检测');
        });
    </script>
</body>
</html>
