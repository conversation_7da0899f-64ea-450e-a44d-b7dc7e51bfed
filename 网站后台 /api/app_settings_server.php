<?php
/**
 * APP设置API接口 - 服务器环境版本
 * 处理APP弹窗、协议、更新等功能的API请求
 * 适用于服务器部署环境
 */

// 定义API访问常量
define('API_ACCESS', true);

// 设置响应头
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入必要的文件
require_once __DIR__ . '/../includes/functions.php';

// 数据库连接
try {
    require_once __DIR__ . '/../includes/db.php';
    $database_available = is_database_available();
} catch (Exception $e) {
    $database_available = false;
    error_log('数据库连接失败: ' . $e->getMessage());
}

/**
 * APP设置API处理类
 */
class AppSettingsAPI {
    private $db;
    private $method;
    private $endpoint;
    private $params;
    
    public function __construct() {
        global $pdo, $database_available;

        $this->db = $pdo;
        $this->method = $_SERVER['REQUEST_METHOD'];
        $this->parseUrl();

        // 如果数据库可用，初始化表
        if ($database_available && $this->db) {
            $this->initializeTables();
        }
    }
    
    /**
     * 解析URL获取endpoint和参数
     */
    private function parseUrl() {
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        // 移除多种可能的路径前缀
        $patterns = [
            '/api/app_settings.php/',
            '/api/app_settings/',
            '/app_settings.php/',
            '/app_settings/'
        ];
        
        foreach ($patterns as $pattern) {
            if (strpos($path, $pattern) !== false) {
                $path = str_replace($pattern, '', $path);
                break;
            }
        }
        
        // 如果路径仍然包含文件名，移除它
        $path = str_replace('app_settings.php', '', $path);
        $path = trim($path, '/');
        
        $pathParts = $path ? explode('/', $path) : [];
        
        $this->endpoint = $pathParts[0] ?? '';
        $this->params = array_slice($pathParts, 1);
    }
    
    /**
     * 初始化数据库表
     */
    private function initializeTables() {
        try {
            // 创建弹窗表
            $sql = "CREATE TABLE IF NOT EXISTS app_popups (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                type JSON NOT NULL,
                custom_days INT DEFAULT NULL,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $this->db->exec($sql);

            // 检查并更新现有表结构
            try {
                // 检查type字段是否为ENUM类型，如果是则需要更新
                $stmt = $this->db->query("SHOW COLUMNS FROM app_popups LIKE 'type'");
                $column = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($column && strpos($column['Type'], 'enum') !== false) {
                    // 更新type字段为JSON类型
                    $this->db->exec("ALTER TABLE app_popups MODIFY COLUMN type JSON NOT NULL");

                    // 添加custom_days字段（如果不存在）
                    $stmt = $this->db->query("SHOW COLUMNS FROM app_popups LIKE 'custom_days'");
                    if (!$stmt->fetch()) {
                        $this->db->exec("ALTER TABLE app_popups ADD COLUMN custom_days INT DEFAULT NULL AFTER type");
                    }

                    // 更新现有数据
                    $this->db->exec("UPDATE app_popups SET type = JSON_ARRAY(type) WHERE JSON_VALID(type) = 0");
                }
            } catch (Exception $e) {
                error_log("更新弹窗表结构失败: " . $e->getMessage());
            }
            
            // 创建协议表
            $sql = "CREATE TABLE IF NOT EXISTS app_agreements (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content LONGTEXT NOT NULL,
                status ENUM('draft', 'published') DEFAULT 'draft',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $this->db->exec($sql);
            
            // 创建APP更新表
            $sql = "CREATE TABLE IF NOT EXISTS app_updates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                version VARCHAR(50) NULL DEFAULT '1.0.0',
                title VARCHAR(255) NULL DEFAULT '新版本更新',
                description TEXT NULL DEFAULT '本次更新包含性能优化和bug修复',
                exe_file VARCHAR(255) NULL,
                dmg_file VARCHAR(255) NULL,
                force_update BOOLEAN DEFAULT TRUE,
                status ENUM('draft', 'published') DEFAULT 'draft',
                download_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $this->db->exec($sql);

            // 更新现有表结构以支持新的默认值
            try {
                // 修改现有字段为可选并设置默认值
                $this->db->exec("ALTER TABLE app_updates
                    MODIFY COLUMN version VARCHAR(50) NULL DEFAULT '1.0.0',
                    MODIFY COLUMN title VARCHAR(255) NULL DEFAULT '新版本更新',
                    MODIFY COLUMN description TEXT NULL DEFAULT '本次更新包含性能优化和bug修复',
                    MODIFY COLUMN force_update BOOLEAN DEFAULT TRUE");
            } catch (Exception $e) {
                error_log("更新app_updates表结构失败: " . $e->getMessage());
            }
        } catch (Exception $e) {
            error_log("初始化数据库表失败: " . $e->getMessage());
        }
    }
    
    /**
     * 处理API请求
     */
    public function handleRequest() {
        try {
            // 检查数据库连接
            if (!$this->db) {
                return $this->sendError('Database connection failed', 500);
            }
            
            switch ($this->endpoint) {
                case 'popup':
                    return $this->handlePopup();
                case 'agreement':
                    return $this->handleAgreement();
                case 'update':
                    return $this->handleUpdate();
                case 'test':
                    return $this->handleTest();
                default:
                    return $this->sendError('Invalid endpoint', 404);
            }
        } catch (Exception $e) {
            error_log('APP Settings API Error: ' . $e->getMessage());
            return $this->sendError('Internal server error', 500);
        }
    }
    
    /**
     * 处理测试请求
     */
    private function handleTest() {
        global $database_available;
        
        $testData = [
            'api_status' => 'working',
            'database_status' => $database_available ? 'connected' : 'disconnected',
            'database_type' => 'MySQL',
            'server_time' => date('Y-m-d H:i:s'),
            'endpoints' => ['popup', 'agreement', 'update']
        ];
        
        return $this->sendSuccess($testData, 'API test successful');
    }
    
    /**
     * 处理弹窗相关请求
     */
    private function handlePopup() {
        switch ($this->method) {
            case 'GET':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'list') {
                        return $this->getPopupList();
                    } else {
                        return $this->getPopup($this->params[0]);
                    }
                }
                return $this->sendError('Invalid request', 400);
                
            case 'POST':
                return $this->createPopup();
                
            case 'PUT':
                if (isset($this->params[0])) {
                    return $this->updatePopup($this->params[0]);
                }
                return $this->sendError('ID required', 400);
                
            case 'DELETE':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'all') {
                        return $this->deleteAllPopups();
                    } else {
                        return $this->deletePopup($this->params[0]);
                    }
                }
                return $this->sendError('ID required', 400);
                
            default:
                return $this->sendError('Method not allowed', 405);
        }
    }
    
    /**
     * 获取弹窗列表
     */
    private function getPopupList() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $stmt = $this->db->query("SELECT * FROM app_popups ORDER BY created_at DESC");
            $popups = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // 处理type字段，确保返回正确格式
            foreach ($popups as &$popup) {
                if (isset($popup['type'])) {
                    $popup['type'] = json_decode($popup['type'], true);
                    if (!is_array($popup['type'])) {
                        $popup['type'] = [$popup['type']];
                    }
                }
            }

            return $this->sendSuccess(['popups' => $popups]);
        } catch (Exception $e) {
            error_log('获取弹窗列表失败: ' . $e->getMessage());
            return $this->sendError('Failed to get popup list', 500);
        }
    }
    
    /**
     * 获取单个弹窗
     */
    private function getPopup($id) {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $stmt = $this->db->prepare("SELECT * FROM app_popups WHERE id = ?");
            $stmt->execute([$id]);
            $popup = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$popup) {
                return $this->sendError('Popup not found', 404);
            }

            // 处理type字段，确保返回正确格式
            if (isset($popup['type'])) {
                $popup['type'] = json_decode($popup['type'], true);
                if (!is_array($popup['type'])) {
                    $popup['type'] = [$popup['type']];
                }
            }

            return $this->sendSuccess(['popup' => $popup]);
        } catch (Exception $e) {
            error_log('获取弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to get popup', 500);
        }
    }
    
    /**
     * 创建弹窗
     */
    private function createPopup() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $data = $this->getRequestData();

            if (empty($data['title']) || empty($data['content'])) {
                return $this->sendError('Missing required fields', 400);
            }

            // 处理type数组
            $types = [];
            if (isset($data['type']) && is_array($data['type'])) {
                $types = $data['type'];
            } else {
                return $this->sendError('Invalid type format', 400);
            }

            // 验证类型
            $validTypes = ['once', 'daily_7', 'weekly', 'always', 'custom'];
            foreach ($types as $type) {
                if (!in_array($type, $validTypes)) {
                    return $this->sendError('Invalid type: ' . $type, 400);
                }
            }

            // 处理自定义天数
            $customDays = null;
            if (in_array('custom', $types) && isset($data['custom_days'])) {
                $customDays = intval($data['custom_days']);
                if ($customDays < 1 || $customDays > 365) {
                    return $this->sendError('Invalid custom days', 400);
                }
            }

            $stmt = $this->db->prepare("INSERT INTO app_popups (title, content, type, custom_days) VALUES (?, ?, ?, ?)");
            $success = $stmt->execute([
                $data['title'],
                $data['content'],
                json_encode($types),
                $customDays
            ]);

            if ($success) {
                return $this->sendSuccess(['id' => $this->db->lastInsertId()], 'Popup created successfully');
            } else {
                return $this->sendError('Failed to create popup', 500);
            }
        } catch (Exception $e) {
            error_log('创建弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to create popup: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 处理协议相关请求
     */
    private function handleAgreement() {
        switch ($this->method) {
            case 'GET':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'list') {
                        return $this->getAgreementList();
                    } else {
                        return $this->getAgreement($this->params[0]);
                    }
                }
                return $this->sendError('Invalid request', 400);
                
            case 'POST':
                return $this->createAgreement();
                
            default:
                return $this->sendError('Method not allowed', 405);
        }
    }
    
    /**
     * 获取协议列表
     */
    private function getAgreementList() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $stmt = $this->db->query("SELECT * FROM app_agreements ORDER BY created_at DESC");
            $agreements = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return $this->sendSuccess(['agreements' => $agreements]);
        } catch (Exception $e) {
            error_log('获取协议列表失败: ' . $e->getMessage());
            return $this->sendError('Failed to get agreement list', 500);
        }
    }
    
    /**
     * 处理更新相关请求
     */
    private function handleUpdate() {
        switch ($this->method) {
            case 'GET':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'list') {
                        return $this->getUpdateList();
                    } else {
                        return $this->getUpdate($this->params[0]);
                    }
                }
                return $this->sendError('Invalid request', 400);
                
            case 'POST':
                return $this->createUpdate();
                
            default:
                return $this->sendError('Method not allowed', 405);
        }
    }
    
    /**
     * 获取更新列表
     */
    private function getUpdateList() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $stmt = $this->db->query("SELECT * FROM app_updates ORDER BY created_at DESC");
            $versions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return $this->sendSuccess(['versions' => $versions]);
        } catch (Exception $e) {
            error_log('获取更新列表失败: ' . $e->getMessage());
            return $this->sendError('Failed to get update list', 500);
        }
    }
    
    /**
     * 获取请求数据
     */
    private function getRequestData() {
        if ($this->method === 'GET') {
            return $_GET;
        } else {
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            return $data ?: $_POST;
        }
    }
    
    /**
     * 发送成功响应
     */
    private function sendSuccess($data = null, $message = 'Success') {
        $response = ['success' => true, 'message' => $message];
        if ($data) {
            $response = array_merge($response, $data);
        }
        echo json_encode($response);
        return true;
    }
    
    /**
     * 发送错误响应
     */
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode(['success' => false, 'message' => $message]);
        return false;
    }
}

// 处理请求
try {
    $api = new AppSettingsAPI();
    $api->handleRequest();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
    error_log('APP Settings API Fatal Error: ' . $e->getMessage());
}
?>