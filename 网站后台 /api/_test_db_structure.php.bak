<?php
/**
 * 数据库结构检查API - 本地测试版本
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

try {
    // 模拟数据库结构 - 基于常见的字段名
    $mock_database_fields = [
        'id', 'version', 'title', 'description', 'created_at', 
        'status', 'platform', 'force_update', 'download_count',
        'windows_download_url', 'macos_download_url', 'release_notes'
    ];
    
    // 模拟一些测试数据
    $mock_data = [
        [
            'id' => 1,
            'version' => '3.0.0',
            'title' => '小梅花AI智能客服 v3.0.0',
            'description' => '当前稳定版本',
            'status' => 'published',
            'platform' => 'all',
            'force_update' => 0,
            'windows_download_url' => null,
            'macos_download_url' => null,
            'created_at' => '2025-08-04 09:00:00'
        ],
        [
            'id' => 2,
            'version' => '4.0.0',
            'title' => '小梅花AI智能客服 v4.0.0',
            'description' => '新版本更新',
            'status' => 'published',
            'platform' => 'macos',
            'force_update' => 1,
            'windows_download_url' => null,
            'macos_download_url' => 'https://example.com/update-4.0.0.dmg',
            'created_at' => '2025-08-04 08:30:00'
        ]
    ];
    
    echo json_encode([
        'success' => true,
        'message' => '本地测试数据库',
        'database_fields' => $mock_database_fields,
        'sample_data' => $mock_data,
        'notes' => '这是本地测试环境，模拟真实数据库结构',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>