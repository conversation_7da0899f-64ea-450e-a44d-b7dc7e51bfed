<?php
/**
 * 数据库诊断脚本
 * 检查数据库连接和表结构状态
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 数据库配置
$db_config = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'dbname' => 'xiaomeihuakefu_c',
    'username' => 'xiaomeihuakefu_c',
    'password' => '7Da5F1Xx995cxYz8',
    'charset' => 'utf8mb4'
];

echo "<h1>数据库诊断报告</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; }
</style>\n";

try {
    echo "<h2>1. 数据库连接测试</h2>\n";
    
    // 创建数据库连接
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>\n";
    
    // 检查数据库版本
    $stmt = $pdo->query("SELECT VERSION() as version");
    $version = $stmt->fetch();
    echo "<p class='info'>📊 MySQL版本: {$version['version']}</p>\n";
    
    echo "<h2>2. 表结构检查</h2>\n";
    
    // 检查关键表是否存在
    $requiredTables = ['license_keys', 'license_key_stores', 'admin_users', 'scripts'];
    $existingTables = [];
    
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p class='info'>📋 数据库中共有 " . count($tables) . " 个表</p>\n";
    
    echo "<table>\n";
    echo "<tr><th>表名</th><th>状态</th></tr>\n";
    
    foreach ($requiredTables as $table) {
        if (in_array($table, $tables)) {
            echo "<tr><td>$table</td><td class='success'>✅ 存在</td></tr>\n";
            $existingTables[] = $table;
        } else {
            echo "<tr><td>$table</td><td class='error'>❌ 不存在</td></tr>\n";
        }
    }
    echo "</table>\n";
    
    echo "<h2>3. 抖店支持功能检查</h2>\n";
    
    if (in_array('license_keys', $existingTables)) {
        // 检查license_keys表的抖店字段
        $stmt = $pdo->query("SHOW COLUMNS FROM license_keys");
        $columns = $stmt->fetchAll();
        
        $douyinFields = ['douyin_store_name', 'douyin_store_id'];
        $existingDouyinFields = [];
        
        echo "<h3>license_keys表字段检查</h3>\n";
        echo "<table>\n";
        echo "<tr><th>字段名</th><th>状态</th><th>类型</th><th>默认值</th></tr>\n";
        
        foreach ($columns as $column) {
            if (in_array($column['Field'], $douyinFields)) {
                echo "<tr><td>{$column['Field']}</td><td class='success'>✅ 存在</td><td>{$column['Type']}</td><td>{$column['Default']}</td></tr>\n";
                $existingDouyinFields[] = $column['Field'];
            }
        }
        
        foreach ($douyinFields as $field) {
            if (!in_array($field, $existingDouyinFields)) {
                echo "<tr><td>$field</td><td class='error'>❌ 不存在</td><td>-</td><td>-</td></tr>\n";
            }
        }
        echo "</table>\n";
    }
    
    if (in_array('license_key_stores', $existingTables)) {
        // 检查license_key_stores表的抖店字段
        $stmt = $pdo->query("SHOW COLUMNS FROM license_key_stores");
        $columns = $stmt->fetchAll();
        
        $storeFields = ['store_type', 'douyin_store_name', 'douyin_store_id'];
        $existingStoreFields = [];
        
        echo "<h3>license_key_stores表字段检查</h3>\n";
        echo "<table>\n";
        echo "<tr><th>字段名</th><th>状态</th><th>类型</th><th>默认值</th></tr>\n";
        
        foreach ($columns as $column) {
            if (in_array($column['Field'], $storeFields)) {
                echo "<tr><td>{$column['Field']}</td><td class='success'>✅ 存在</td><td>{$column['Type']}</td><td>{$column['Default']}</td></tr>\n";
                $existingStoreFields[] = $column['Field'];
            }
        }
        
        foreach ($storeFields as $field) {
            if (!in_array($field, $existingStoreFields)) {
                echo "<tr><td>$field</td><td class='error'>❌ 不存在</td><td>-</td><td>-</td></tr>\n";
            }
        }
        echo "</table>\n";
    }
    
    echo "<h2>4. 索引检查</h2>\n";
    
    if (in_array('license_keys', $existingTables)) {
        $stmt = $pdo->query("SHOW INDEX FROM license_keys");
        $indexes = $stmt->fetchAll();
        
        $douyinIndexes = ['idx_license_keys_douyin_store_id'];
        $existingIndexes = array_column($indexes, 'Key_name');
        
        echo "<table>\n";
        echo "<tr><th>索引名</th><th>状态</th></tr>\n";
        
        foreach ($douyinIndexes as $index) {
            if (in_array($index, $existingIndexes)) {
                echo "<tr><td>$index</td><td class='success'>✅ 存在</td></tr>\n";
            } else {
                echo "<tr><td>$index</td><td class='error'>❌ 不存在</td></tr>\n";
            }
        }
        echo "</table>\n";
    }
    
    echo "<h2>5. 视图检查</h2>\n";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'v_license_keys_with_stores'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ 视图 'v_license_keys_with_stores' 存在</p>\n";
    } else {
        echo "<p class='error'>❌ 视图 'v_license_keys_with_stores' 不存在</p>\n";
    }
    
    echo "<h2>6. 数据统计</h2>\n";
    
    if (in_array('license_keys', $existingTables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM license_keys");
        $result = $stmt->fetch();
        echo "<p class='info'>📊 卡密总数: {$result['count']}</p>\n";
        
        // 检查是否有抖店数据
        if (in_array('douyin_store_name', $existingDouyinFields ?? [])) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM license_keys WHERE douyin_store_name IS NOT NULL");
            $result = $stmt->fetch();
            echo "<p class='info'>📊 有抖店信息的卡密: {$result['count']}</p>\n";
        }
    }
    
    if (in_array('license_key_stores', $existingTables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM license_key_stores");
        $result = $stmt->fetch();
        echo "<p class='info'>📊 额外店铺总数: {$result['count']}</p>\n";
        
        // 检查店铺类型分布
        if (in_array('store_type', $existingStoreFields ?? [])) {
            $stmt = $pdo->query("SELECT store_type, COUNT(*) as count FROM license_key_stores GROUP BY store_type");
            $results = $stmt->fetchAll();
            
            echo "<h3>店铺类型分布</h3>\n";
            echo "<table>\n";
            echo "<tr><th>店铺类型</th><th>数量</th></tr>\n";
            foreach ($results as $result) {
                echo "<tr><td>{$result['store_type']}</td><td>{$result['count']}</td></tr>\n";
            }
            echo "</table>\n";
        }
    }
    
    echo "<h2>7. 建议操作</h2>\n";
    
    $missingDouyinFields = array_diff($douyinFields ?? [], $existingDouyinFields ?? []);
    $missingStoreFields = array_diff($storeFields ?? [], $existingStoreFields ?? []);
    
    if (!empty($missingDouyinFields) || !empty($missingStoreFields)) {
        echo "<p class='warning'>⚠️ 检测到缺少抖店支持字段，建议执行以下操作：</p>\n";
        echo "<ol>\n";
        echo "<li>运行导入脚本: <code>php import_douyin_store_support.php</code></li>\n";
        echo "<li>或者手动执行SQL文件: <code>09_database_douyin_store_support_fixed.sql</code></li>\n";
        echo "</ol>\n";
    } else {
        echo "<p class='success'>✅ 抖店支持功能已完整安装</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 诊断过程中发生错误: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

echo "<hr>\n";
echo "<p><small>诊断完成时间: " . date('Y-m-d H:i:s') . "</small></p>\n";
?>
