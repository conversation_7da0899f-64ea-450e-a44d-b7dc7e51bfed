<?php
/**
 * 抖店功能导入修复脚本
 * 专门处理09_database_douyin_store_support.sql导入异常问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(300);

// 数据库配置
$db_config = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'dbname' => 'xiaomeihuakefu_c',
    'username' => 'xiaomeihuakefu_c',
    'password' => '7Da5F1Xx995cxYz8',
    'charset' => 'utf8mb4'
];

function logMessage($message, $type = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    echo "[{$timestamp}] [{$type}] {$message}\n";
    flush();
}

try {
    logMessage("开始修复抖店功能导入问题...");
    
    // 创建数据库连接
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    logMessage("数据库连接成功");
    
    // 开始事务
    $pdo->beginTransaction();
    
    try {
        // 1. 为 license_keys 表添加抖店相关字段
        logMessage("步骤1: 为 license_keys 表添加抖店字段...");
        
        // 检查字段是否已存在
        $stmt = $pdo->query("SHOW COLUMNS FROM license_keys LIKE 'douyin_store_name'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("ALTER TABLE license_keys ADD COLUMN douyin_store_name TEXT DEFAULT NULL");
            logMessage("✅ 添加 douyin_store_name 字段成功");
        } else {
            logMessage("⚠️ douyin_store_name 字段已存在，跳过");
        }
        
        $stmt = $pdo->query("SHOW COLUMNS FROM license_keys LIKE 'douyin_store_id'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("ALTER TABLE license_keys ADD COLUMN douyin_store_id TEXT DEFAULT NULL");
            logMessage("✅ 添加 douyin_store_id 字段成功");
        } else {
            logMessage("⚠️ douyin_store_id 字段已存在，跳过");
        }
        
        // 2. 为 license_key_stores 表添加字段
        logMessage("步骤2: 为 license_key_stores 表添加字段...");
        
        $stmt = $pdo->query("SHOW COLUMNS FROM license_key_stores LIKE 'store_type'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("ALTER TABLE license_key_stores ADD COLUMN store_type TEXT DEFAULT 'wechat'");
            logMessage("✅ 添加 store_type 字段成功");
        } else {
            logMessage("⚠️ store_type 字段已存在，跳过");
        }
        
        $stmt = $pdo->query("SHOW COLUMNS FROM license_key_stores LIKE 'douyin_store_name'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("ALTER TABLE license_key_stores ADD COLUMN douyin_store_name TEXT DEFAULT NULL");
            logMessage("✅ 添加 douyin_store_name 字段成功");
        } else {
            logMessage("⚠️ douyin_store_name 字段已存在，跳过");
        }
        
        $stmt = $pdo->query("SHOW COLUMNS FROM license_key_stores LIKE 'douyin_store_id'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("ALTER TABLE license_key_stores ADD COLUMN douyin_store_id TEXT DEFAULT NULL");
            logMessage("✅ 添加 douyin_store_id 字段成功");
        } else {
            logMessage("⚠️ douyin_store_id 字段已存在，跳过");
        }
        
        // 3. 创建索引
        logMessage("步骤3: 创建索引...");
        
        try {
            $pdo->exec("CREATE INDEX idx_license_keys_douyin_store_id ON license_keys(douyin_store_id)");
            logMessage("✅ 创建 idx_license_keys_douyin_store_id 索引成功");
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                logMessage("⚠️ idx_license_keys_douyin_store_id 索引已存在，跳过");
            } else {
                throw $e;
            }
        }
        
        try {
            $pdo->exec("CREATE INDEX idx_license_key_stores_store_type ON license_key_stores(store_type)");
            logMessage("✅ 创建 idx_license_key_stores_store_type 索引成功");
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                logMessage("⚠️ idx_license_key_stores_store_type 索引已存在，跳过");
            } else {
                throw $e;
            }
        }
        
        try {
            $pdo->exec("CREATE INDEX idx_license_key_stores_douyin_store_id ON license_key_stores(douyin_store_id)");
            logMessage("✅ 创建 idx_license_key_stores_douyin_store_id 索引成功");
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                logMessage("⚠️ idx_license_key_stores_douyin_store_id 索引已存在，跳过");
            } else {
                throw $e;
            }
        }
        
        // 4. 更新现有数据
        logMessage("步骤4: 更新现有数据...");
        
        $stmt = $pdo->prepare("UPDATE license_key_stores SET store_type = 'wechat' WHERE store_type IS NULL");
        $stmt->execute();
        $affectedRows = $stmt->rowCount();
        logMessage("✅ 更新了 $affectedRows 条记录的店铺类型");
        
        // 5. 创建视图
        logMessage("步骤5: 创建视图...");
        
        // 先删除视图（如果存在）
        try {
            $pdo->exec("DROP VIEW IF EXISTS v_license_keys_with_stores");
            logMessage("删除旧视图（如果存在）");
        } catch (PDOException $e) {
            // 忽略错误
        }
        
        // 创建新视图
        $createViewSQL = "
        CREATE VIEW v_license_keys_with_stores AS
        SELECT 
            lk.id,
            lk.key_value,
            lk.type,
            lk.status,
            lk.expiry_date,
            lk.has_customer_service,
            lk.has_product_listing,
            lk.is_multi_store,
            lk.created_at,
            -- 微信店铺信息
            lk.store_name as wechat_store_name,
            lk.wechat_store_id,
            -- 抖店信息
            lk.douyin_store_name,
            lk.douyin_store_id,
            -- 额外店铺数量
            (SELECT COUNT(*) FROM license_key_stores lks WHERE lks.license_key_id = lk.id) as additional_stores_count
        FROM license_keys lk";
        
        $pdo->exec($createViewSQL);
        logMessage("✅ 创建视图 v_license_keys_with_stores 成功");
        
        // 提交事务
        $pdo->commit();
        logMessage("✅ 所有操作已提交");
        
        // 验证结果
        logMessage("步骤6: 验证安装结果...");
        
        // 检查字段
        $stmt = $pdo->query("SHOW COLUMNS FROM license_keys LIKE 'douyin_store_%'");
        $douyinFields = $stmt->fetchAll();
        logMessage("license_keys表抖店字段数量: " . count($douyinFields));
        
        $stmt = $pdo->query("SHOW COLUMNS FROM license_key_stores LIKE 'store_type'");
        if ($stmt->rowCount() > 0) {
            logMessage("✅ license_key_stores表store_type字段存在");
        }
        
        // 检查视图
        $stmt = $pdo->query("SHOW TABLES LIKE 'v_license_keys_with_stores'");
        if ($stmt->rowCount() > 0) {
            logMessage("✅ 视图 v_license_keys_with_stores 存在");
        }
        
        // 检查索引
        $stmt = $pdo->query("SHOW INDEX FROM license_keys WHERE Key_name = 'idx_license_keys_douyin_store_id'");
        if ($stmt->rowCount() > 0) {
            logMessage("✅ 索引 idx_license_keys_douyin_store_id 存在");
        }
        
        logMessage("🎉 抖店功能修复完成！所有组件已成功安装。", "SUCCESS");
        
    } catch (Exception $e) {
        // 回滚事务
        $pdo->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    logMessage("❌ 修复失败: " . $e->getMessage(), "ERROR");
    logMessage("详细错误: " . $e->getTraceAsString(), "ERROR");
    exit(1);
}

logMessage("✨ 修复脚本执行完成！", "SUCCESS");
logMessage("建议运行 diagnose_database.php 验证安装结果");
?>
