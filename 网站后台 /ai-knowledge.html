<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI知识库</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #ffffff;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        .container {
            width: 100%;
            height: 100vh;
            background: #ffffff;
            position: relative;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- AI知识库页面 - 等待脚本注入 -->
    </div>

    <script>
        // 检查是否在APP环境中
        function isInApp() {
            const userAgent = navigator.userAgent;
            return userAgent.includes('xiaomeihua-app') ||
                   userAgent.includes('Electron') ||
                   (userAgent.includes('Chrome') && (typeof window.process !== 'undefined' ||
                    typeof window.electron !== 'undefined' ||
                    typeof window.require !== 'undefined'));
        }

        // 为APP提供的API接口
        window.aiKnowledgeAPI = {
            getStatus: function() {
                return {
                    loaded: true,
                    isApp: isInApp(),
                    hasTampermonkey: !!(window.xiaomeihuaAI || window.xiaomeihuaAIKnowledge || window.GM_setValue),
                    timestamp: Date.now()
                };
            },

            checkTampermonkey: function() {
                console.log('Tampermonkey脚本检查完成');
            }
        };

        console.log('AI知识库页面已加载，等待脚本注入...');
    </script>
</body>
</html>